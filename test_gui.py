#!/usr/bin/env python3
"""
Test script for GUI functionality
"""

def test_gui_dependencies():
    """Test các dependencies cần thiết cho GUI"""
    print("🧪 Testing GUI Dependencies...")
    print("=" * 40)
    
    # Test tkinter
    try:
        import tkinter as tk
        print("✅ Tkinter: OK")
        
        # Test basic tkinter functionality
        root = tk.Tk()
        root.withdraw()  # Hide window
        root.destroy()
        print("✅ Tkinter basic functionality: OK")
        
    except ImportError:
        print("❌ Tkinter: Not available")
        return False
    except Exception as e:
        print(f"❌ Tkinter error: {e}")
        return False
    
    # Test PIL
    try:
        from PIL import Image, ImageTk
        print("✅ PIL (Pillow): OK")
    except ImportError:
        print("❌ PIL: Not available")
        return False
    
    # Test OpenCV
    try:
        import cv2
        print("✅ OpenCV: OK")
    except ImportError:
        print("❌ OpenCV: Not available") 
        return False
    
    # Test YOLO
    try:
        from ultralytics import YOL<PERSON>
        print("✅ Ultralytics YOLO: OK")
    except ImportError:
        print("❌ Ultralytics: Not available")
        return False
    
    # Test model file
    import os
    model_path = "best.pt"
    if os.path.exists(model_path):
        print(f"✅ Model file ({model_path}): Found")
    else:
        print(f"❌ Model file ({model_path}): Not found")
        return False
    
    print("\n🎉 All dependencies OK! GUI should work.")
    return True

def create_test_image():
    """Tạo ảnh test đơn giản"""
    try:
        import numpy as np
        import cv2
        
        # Tạo ảnh test 512x512 với gradient
        img = np.zeros((512, 512, 3), dtype=np.uint8)
        
        # Tạo gradient
        for i in range(512):
            for j in range(512):
                img[i, j] = [i//2, j//2, (i+j)//4]
        
        # Thêm một số hình dạng
        cv2.rectangle(img, (100, 100), (200, 200), (255, 0, 0), -1)
        cv2.circle(img, (350, 150), 50, (0, 255, 0), -1)
        cv2.ellipse(img, (300, 350), (80, 40), 45, 0, 360, (0, 0, 255), -1)
        
        # Lưu ảnh
        cv2.imwrite("test_image.jpg", img)
        print("✅ Created test image: test_image.jpg")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test image: {e}")
        return False

def launch_simple_gui():
    """Khởi chạy GUI đơn giản"""
    try:
        print("\n🚀 Launching Simple GUI...")
        print("(Close the window to continue)")
        
        import subprocess
        import sys
        
        # Chạy GUI
        result = subprocess.run([sys.executable, "simple_gui.py"], 
                              capture_output=False)
        
        if result.returncode == 0:
            print("✅ GUI closed normally")
        else:
            print(f"⚠️  GUI exited with code: {result.returncode}")
            
    except Exception as e:
        print(f"❌ Failed to launch GUI: {e}")

def main():
    """Main function"""
    print("🏥 LUNG CANCER GUI TEST SUITE")
    print("=" * 50)
    
    # Test dependencies
    if not test_gui_dependencies():
        print("\n❌ Dependencies test failed!")
        print("Please install missing packages:")
        print("pip install ultralytics opencv-python pillow")
        return
    
    print("\n" + "=" * 50)
    
    # Create test image
    create_test_image()
    
    print("\n" + "=" * 50)
    
    # Ask user if they want to launch GUI
    while True:
        choice = input("\n🎯 Launch GUI for testing? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            launch_simple_gui()
            break
        elif choice in ['n', 'no']:
            print("👋 Test completed without launching GUI")
            break
        else:
            print("Please enter 'y' or 'n'")
    
    print("\n📋 Test Summary:")
    print("- Dependencies: ✅ All OK")
    print("- Test image: ✅ Created")
    print("- Ready to use GUI!")
    
    print("\n💡 To use the GUI:")
    print("1. Run: python simple_gui.py")
    print("2. Or run: python lung_cancer_gui.py")
    print("3. Select the test_image.jpg file")
    print("4. Click Analyze to test detection")

if __name__ == "__main__":
    main()
