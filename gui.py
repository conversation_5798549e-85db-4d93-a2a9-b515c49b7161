#!/usr/bin/env python3
"""
Simple GUI for Lung Cancer Detection
GUI đơn giản cho dự đoán ung thư phổi

Author: AI Assistant
Date: September 2, 2025
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import requests
import json
from PIL import Image, ImageTk
import cv2
import numpy as np
import threading

class LungCancerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🏥 Lung Cancer Detection System")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # API endpoint
        self.api_url = "http://localhost:8000"
        
        # Variables for form
        self.form_vars = {}
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Main title
        title_label = tk.Label(
            self.root, 
            text="🏥 Lung Cancer Detection System",
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Survey tab
        survey_frame = ttk.Frame(notebook)
        notebook.add(survey_frame, text="📊 Survey Prediction")
        self.create_survey_tab(survey_frame)
        
        # Image tab
        image_frame = ttk.Frame(notebook)
        notebook.add(image_frame, text="🔍 Image Analysis")
        self.create_image_tab(image_frame)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = tk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#e9ecef'
        )
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_survey_tab(self, parent):
        """Create survey prediction tab"""
        # Scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Survey form
        form_frame = ttk.LabelFrame(scrollable_frame, text="Patient Information", padding=10)
        form_frame.pack(fill='x', padx=10, pady=5)
        
        # Form fields
        fields = [
            ("Gender", "gender", ["Female", "Male"]),
            ("Age", "age", None),
            ("Smoking", "smoking", ["No", "Yes"]),
            ("Yellow Fingers", "yellow_fingers", ["No", "Yes"]),
            ("Anxiety", "anxiety", ["No", "Yes"]),
            ("Peer Pressure", "peer_pressure", ["No", "Yes"]),
            ("Chronic Disease", "chronic_disease", ["No", "Yes"]),
            ("Fatigue", "fatigue", ["No", "Yes"]),
            ("Allergy", "allergy", ["No", "Yes"]),
            ("Wheezing", "wheezing", ["No", "Yes"]),
            ("Alcohol Consuming", "alcohol_consuming", ["No", "Yes"]),
            ("Coughing", "coughing", ["No", "Yes"]),
            ("Shortness of Breath", "shortness_of_breath", ["No", "Yes"]),
            ("Swallowing Difficulty", "swallowing_difficulty", ["No", "Yes"]),
            ("Chest Pain", "chest_pain", ["No", "Yes"])
        ]
        
        for i, (label, var_name, options) in enumerate(fields):
            row = i // 2
            col = (i % 2) * 2
            
            tk.Label(form_frame, text=f"{label}:").grid(row=row, column=col, sticky='w', padx=5, pady=2)
            
            if options:
                var = tk.StringVar(value=options[0])
                combo = ttk.Combobox(form_frame, textvariable=var, values=options, state="readonly", width=15)
                combo.grid(row=row, column=col+1, padx=5, pady=2)
            else:
                var = tk.StringVar()
                entry = tk.Entry(form_frame, textvariable=var, width=18)
                entry.grid(row=row, column=col+1, padx=5, pady=2)
            
            self.form_vars[var_name] = var
        
        # Predict button
        predict_btn = tk.Button(
            scrollable_frame,
            text="🔍 Predict Cancer Risk",
            command=self.predict_survey,
            bg='#007bff',
            fg='white',
            font=("Arial", 12, "bold"),
            pady=10
        )
        predict_btn.pack(pady=20)
        
        # Result frame
        self.survey_result_frame = ttk.LabelFrame(scrollable_frame, text="Prediction Result", padding=10)
        self.survey_result_frame.pack(fill='x', padx=10, pady=5)
        
        self.survey_result_text = tk.Text(self.survey_result_frame, height=8, wrap=tk.WORD)
        self.survey_result_text.pack(fill='both', expand=True)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_image_tab(self, parent):
        """Create image analysis tab"""
        # File selection
        file_frame = ttk.LabelFrame(parent, text="Select CT Scan Image", padding=10)
        file_frame.pack(fill='x', padx=10, pady=5)
        
        self.file_path_var = tk.StringVar()
        tk.Entry(file_frame, textvariable=self.file_path_var, width=60).pack(side='left', padx=5)
        tk.Button(file_frame, text="Browse", command=self.browse_file).pack(side='right', padx=5)
        
        # Analyze button
        analyze_btn = tk.Button(
            parent,
            text="🔍 Analyze Image",
            command=self.analyze_image,
            bg='#28a745',
            fg='white',
            font=("Arial", 12, "bold"),
            pady=10
        )
        analyze_btn.pack(pady=10)
        
        # Result frame
        self.image_result_frame = ttk.LabelFrame(parent, text="Analysis Result", padding=10)
        self.image_result_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.image_result_text = tk.Text(self.image_result_frame, height=10, wrap=tk.WORD)
        self.image_result_text.pack(fill='both', expand=True)
    
    def browse_file(self):
        """Browse for image file"""
        file_path = filedialog.askopenfilename(
            title="Select CT Scan Image",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.file_path_var.set(file_path)
    
    def predict_survey(self):
        """Predict cancer from survey data"""
        def run_prediction():
            try:
                self.status_var.set("Making prediction...")
                
                # Prepare data
                data = {}
                for var_name, var in self.form_vars.items():
                    value = var.get()
                    if var_name == "age":
                        try:
                            data[var_name] = int(value)
                        except ValueError:
                            messagebox.showerror("Error", "Age must be a number")
                            return
                    else:
                        # Convert Yes/No to 1/0, Male/Female to 1/0
                        if value in ["Yes", "Male"]:
                            data[var_name] = 1
                        else:
                            data[var_name] = 0
                
                # Make API request
                response = requests.post(f"{self.api_url}/predict/survey", data=data)
                
                if response.status_code == 200:
                    result = response.json()
                    self.display_survey_result(result)
                    self.status_var.set("Prediction completed")
                else:
                    messagebox.showerror("Error", f"API Error: {response.text}")
                    self.status_var.set("Prediction failed")
                    
            except requests.exceptions.ConnectionError:
                messagebox.showerror("Error", "Cannot connect to API. Make sure the server is running.")
                self.status_var.set("Connection failed")
            except Exception as e:
                messagebox.showerror("Error", f"Prediction failed: {str(e)}")
                self.status_var.set("Prediction failed")
        
        # Run in separate thread
        threading.Thread(target=run_prediction, daemon=True).start()
    
    def analyze_image(self):
        """Analyze CT scan image"""
        def run_analysis():
            try:
                file_path = self.file_path_var.get()
                if not file_path:
                    messagebox.showerror("Error", "Please select an image file")
                    return
                
                self.status_var.set("Analyzing image...")
                
                # Prepare file for upload
                with open(file_path, 'rb') as f:
                    files = {'file': f}
                    response = requests.post(f"{self.api_url}/predict/image", files=files)
                
                if response.status_code == 200:
                    result = response.json()
                    self.display_image_result(result)
                    self.status_var.set("Analysis completed")
                else:
                    messagebox.showerror("Error", f"API Error: {response.text}")
                    self.status_var.set("Analysis failed")
                    
            except requests.exceptions.ConnectionError:
                messagebox.showerror("Error", "Cannot connect to API. Make sure the server is running.")
                self.status_var.set("Connection failed")
            except Exception as e:
                messagebox.showerror("Error", f"Analysis failed: {str(e)}")
                self.status_var.set("Analysis failed")
        
        # Run in separate thread
        threading.Thread(target=run_analysis, daemon=True).start()
    
    def display_survey_result(self, result):
        """Display survey prediction result"""
        self.survey_result_text.delete(1.0, tk.END)
        
        text = f"🏥 LUNG CANCER PREDICTION RESULT\n"
        text += "=" * 40 + "\n\n"
        text += f"📊 Prediction: {result['prediction']}\n"
        text += f"⚠️ Risk Level: {result['risk_level']}\n\n"
        
        if result.get('confidence'):
            conf = result['confidence']
            text += f"📈 Confidence Scores:\n"
            text += f"   • No Cancer: {conf['no_cancer']:.3f} ({conf['no_cancer']*100:.1f}%)\n"
            text += f"   • Cancer: {conf['cancer']:.3f} ({conf['cancer']*100:.1f}%)\n\n"
        
        text += f"🕒 Timestamp: {result['timestamp']}\n"
        
        # Add recommendation
        if result['prediction'] == 'YES':
            text += "\n⚠️ RECOMMENDATION:\n"
            text += "Please consult with a healthcare professional immediately for further examination."
        else:
            text += "\n✅ RECOMMENDATION:\n"
            text += "Low risk detected, but regular health checkups are still recommended."
        
        self.survey_result_text.insert(tk.END, text)
    
    def display_image_result(self, result):
        """Display image analysis result"""
        self.image_result_text.delete(1.0, tk.END)
        
        text = f"🔍 CT SCAN ANALYSIS RESULT\n"
        text += "=" * 40 + "\n\n"
        text += f"📊 Status: {result['status'].replace('_', ' ').title()}\n"
        text += f"🎯 Total Detections: {result['total_detections']}\n\n"
        
        if result['detections']:
            text += "🔍 DETECTED CANCER TYPES:\n"
            for i, detection in enumerate(result['detections'], 1):
                text += f"   {i}. {detection['cancer_type']}\n"
                text += f"      Confidence: {detection['confidence']:.3f} ({detection['confidence']*100:.1f}%)\n"
        else:
            text += "✅ No cancer detected in the image.\n"
        
        text += f"\n🕒 Timestamp: {result['timestamp']}\n"
        
        # Add recommendation
        if result['detections']:
            text += "\n⚠️ RECOMMENDATION:\n"
            text += "Cancer detected in CT scan. Please consult with an oncologist immediately."
        else:
            text += "\n✅ RECOMMENDATION:\n"
            text += "No cancer detected, but continue regular medical checkups."
        
        self.image_result_text.insert(tk.END, text)

def main():
    """Main function"""
    root = tk.Tk()
    app = LungCancerGUI(root)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
