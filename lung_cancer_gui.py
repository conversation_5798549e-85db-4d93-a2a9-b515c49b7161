#!/usr/bin/env python3
"""
Giao diện GUI cho hệ thống nhận diện ung thư phổi
Lung Cancer Detection GUI with Bounding Boxes

Author: AI Assistant
Date: September 2, 2025
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import cv2
import numpy as np
from PIL import Image, ImageTk
from ultralytics import YOLO
import os
from datetime import datetime
import threading

class LungCancerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🏥 Lung Cancer Detection System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Model và biến
        self.model = None
        self.model_path = "best.pt"
        self.current_image = None
        self.original_image = None
        self.conf_threshold = 0.25
        
        # <PERSON><PERSON><PERSON> sắc cho các loại ung thư
        self.colors = {
            "Adenocarcinoma": (255, 0, 0),          # Đỏ
            "Small Cell Carcinoma": (0, 255, 0),    # <PERSON>anh lá
            "Large Cell Carcinoma": (0, 0, 255),    # <PERSON><PERSON><PERSON> dương
            "Squamous Cell Carcinoma": (255, 255, 0) # Vàng
        }
        
        self.setup_ui()
        self.load_model()
    
    def setup_ui(self):
        """Thiết lập giao diện"""
        # Header
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="🏥 Lung Cancer Detection System", 
                              font=('Arial', 20, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(pady=20)
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10)
        
        # Left panel - Controls
        left_frame = tk.Frame(main_frame, bg='#ecf0f1', width=300)
        left_frame.pack(side='left', fill='y', padx=(0, 10))
        left_frame.pack_propagate(False)
        
        self.setup_controls(left_frame)
        
        # Right panel - Image display and results
        right_frame = tk.Frame(main_frame, bg='#ffffff')
        right_frame.pack(side='right', fill='both', expand=True)
        
        self.setup_image_panel(right_frame)
    
    def setup_controls(self, parent):
        """Thiết lập panel điều khiển"""
        # Model status
        status_frame = tk.LabelFrame(parent, text="📊 Model Status", font=('Arial', 12, 'bold'), 
                                   bg='#ecf0f1', padx=10, pady=10)
        status_frame.pack(fill='x', pady=10)
        
        self.status_label = tk.Label(status_frame, text="🔄 Loading model...", 
                                   font=('Arial', 10), bg='#ecf0f1')
        self.status_label.pack()
        
        # File selection
        file_frame = tk.LabelFrame(parent, text="📁 File Selection", font=('Arial', 12, 'bold'),
                                 bg='#ecf0f1', padx=10, pady=10)
        file_frame.pack(fill='x', pady=10)
        
        self.select_btn = tk.Button(file_frame, text="Select CT Scan Image", 
                                  command=self.select_image, font=('Arial', 11),
                                  bg='#3498db', fg='white', relief='flat', pady=5)
        self.select_btn.pack(fill='x', pady=5)
        
        self.file_label = tk.Label(file_frame, text="No file selected", 
                                 font=('Arial', 9), bg='#ecf0f1', fg='#7f8c8d')
        self.file_label.pack()
        
        # Settings
        settings_frame = tk.LabelFrame(parent, text="⚙️ Settings", font=('Arial', 12, 'bold'),
                                     bg='#ecf0f1', padx=10, pady=10)
        settings_frame.pack(fill='x', pady=10)
        
        # Confidence threshold
        tk.Label(settings_frame, text="Confidence Threshold:", 
               font=('Arial', 10), bg='#ecf0f1').pack(anchor='w')
        
        self.conf_var = tk.DoubleVar(value=0.25)
        self.conf_scale = tk.Scale(settings_frame, from_=0.1, to=1.0, resolution=0.05,
                                 orient='horizontal', variable=self.conf_var,
                                 bg='#ecf0f1', font=('Arial', 9))
        self.conf_scale.pack(fill='x', pady=5)
        
        # Show confidence checkbox
        self.show_conf_var = tk.BooleanVar(value=True)
        self.show_conf_cb = tk.Checkbutton(settings_frame, text="Show confidence scores",
                                         variable=self.show_conf_var, bg='#ecf0f1',
                                         font=('Arial', 9))
        self.show_conf_cb.pack(anchor='w', pady=2)
        
        # Show class names checkbox
        self.show_names_var = tk.BooleanVar(value=True)
        self.show_names_cb = tk.Checkbutton(settings_frame, text="Show class names",
                                          variable=self.show_names_var, bg='#ecf0f1',
                                          font=('Arial', 9))
        self.show_names_cb.pack(anchor='w', pady=2)
        
        # Analyze button
        self.analyze_btn = tk.Button(parent, text="🔍 Analyze Image", 
                                   command=self.analyze_image, font=('Arial', 12, 'bold'),
                                   bg='#e74c3c', fg='white', relief='flat', pady=10,
                                   state='disabled')
        self.analyze_btn.pack(fill='x', pady=20)
        
        # Results summary
        results_frame = tk.LabelFrame(parent, text="📋 Detection Summary", 
                                    font=('Arial', 12, 'bold'), bg='#ecf0f1',
                                    padx=10, pady=10)
        results_frame.pack(fill='both', expand=True, pady=10)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=10, width=35,
                                                    font=('Courier', 9), bg='white',
                                                    wrap=tk.WORD)
        self.results_text.pack(fill='both', expand=True)
        
        # Clear button
        self.clear_btn = tk.Button(parent, text="🗑️ Clear Results", 
                                 command=self.clear_results, font=('Arial', 10),
                                 bg='#95a5a6', fg='white', relief='flat', pady=5)
        self.clear_btn.pack(fill='x', pady=5)
    
    def setup_image_panel(self, parent):
        """Thiết lập panel hiển thị ảnh"""
        # Image display frame
        img_frame = tk.LabelFrame(parent, text="🖼️ CT Scan Analysis", 
                                font=('Arial', 12, 'bold'), bg='#ffffff',
                                padx=10, pady=10)
        img_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Canvas for image
        self.canvas = tk.Canvas(img_frame, bg='#f8f9fa', relief='sunken', bd=2)
        self.canvas.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Scrollbars for canvas
        h_scroll = tk.Scrollbar(img_frame, orient='horizontal', command=self.canvas.xview)
        v_scroll = tk.Scrollbar(img_frame, orient='vertical', command=self.canvas.yview)
        self.canvas.configure(xscrollcommand=h_scroll.set, yscrollcommand=v_scroll.set)
        
        # Placeholder text
        self.canvas.create_text(400, 300, text="Select an image to begin analysis", 
                              font=('Arial', 16), fill='#bdc3c7')
        
        # Legend frame
        legend_frame = tk.Frame(parent, bg='#ffffff')
        legend_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        self.setup_legend(legend_frame)
    
    def setup_legend(self, parent):
        """Thiết lập chú thích màu sắc"""
        legend_label = tk.Label(parent, text="🎨 Color Legend:", 
                              font=('Arial', 11, 'bold'), bg='#ffffff')
        legend_label.pack(side='left', padx=10)
        
        colors_info = [
            ("Adenocarcinoma", "#FF0000"),
            ("Small Cell Carcinoma", "#00FF00"),
            ("Large Cell Carcinoma", "#0000FF"),
            ("Squamous Cell Carcinoma", "#FFFF00")
        ]
        
        for name, color in colors_info:
            frame = tk.Frame(parent, bg='#ffffff')
            frame.pack(side='left', padx=5)
            
            color_box = tk.Label(frame, text="  ", bg=color, relief='solid', bd=1)
            color_box.pack(side='left')
            
            text_label = tk.Label(frame, text=name, font=('Arial', 9), bg='#ffffff')
            text_label.pack(side='left', padx=(2, 0))
    
    def load_model(self):
        """Tải model YOLO"""
        def load():
            try:
                if os.path.exists(self.model_path):
                    self.model = YOLO(self.model_path)
                    self.root.after(0, lambda: self.status_label.config(
                        text="✅ Model loaded successfully", fg='#27ae60'))
                    self.root.after(0, lambda: self.update_results_text(
                        "🏥 LUNG CANCER DETECTION SYSTEM\n" +
                        "=" * 40 + "\n" +
                        f"Model: {self.model_path}\n" +
                        f"Classes: {list(self.model.names.values())}\n" +
                        f"Ready for analysis!\n\n"))
                else:
                    self.root.after(0, lambda: self.status_label.config(
                        text="❌ Model file not found", fg='#e74c3c'))
                    self.root.after(0, lambda: messagebox.showerror(
                        "Error", f"Model file not found: {self.model_path}"))
            except Exception as e:
                self.root.after(0, lambda: self.status_label.config(
                    text="❌ Model loading failed", fg='#e74c3c'))
                self.root.after(0, lambda: messagebox.showerror(
                    "Error", f"Failed to load model: {str(e)}"))
        
        threading.Thread(target=load, daemon=True).start()
    
    def select_image(self):
        """Chọn ảnh CT scan"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Select CT Scan Image",
            filetypes=file_types,
            initialdir=os.path.expanduser("~/Desktop")
        )
        
        if filename:
            self.load_image(filename)
    
    def load_image(self, filepath):
        """Tải và hiển thị ảnh"""
        try:
            # Load image with OpenCV
            self.original_image = cv2.imread(filepath)
            if self.original_image is None:
                messagebox.showerror("Error", "Cannot load image file")
                return
            
            # Convert BGR to RGB for display
            rgb_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
            self.current_image = rgb_image.copy()
            
            # Display image
            self.display_image(self.current_image)
            
            # Update UI
            self.file_label.config(text=os.path.basename(filepath), fg='#2c3e50')
            self.analyze_btn.config(state='normal')
            
            self.update_results_text(f"📁 Loaded: {os.path.basename(filepath)}\n" +
                                   f"Size: {self.original_image.shape[1]}x{self.original_image.shape[0]}\n" +
                                   f"Ready for analysis!\n\n")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
    
    def display_image(self, image):
        """Hiển thị ảnh trên canvas"""
        # Convert to PIL Image
        pil_image = Image.fromarray(image)
        
        # Resize if too large
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width > 1 and canvas_height > 1:  # Canvas has been initialized
            img_width, img_height = pil_image.size
            
            # Calculate scale factor
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            scale = min(scale_x, scale_y, 1.0)  # Don't upscale
            
            if scale < 1.0:
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Convert to PhotoImage
        self.photo = ImageTk.PhotoImage(pil_image)
        
        # Clear canvas and display image
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor='nw', image=self.photo)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def analyze_image(self):
        """Phân tích ảnh với model YOLO"""
        if self.model is None:
            messagebox.showerror("Error", "Model not loaded")
            return
        
        if self.original_image is None:
            messagebox.showerror("Error", "No image loaded")
            return
        
        # Disable button during analysis
        self.analyze_btn.config(state='disabled', text='🔄 Analyzing...')
        
        def analyze():
            try:
                # Run inference
                conf = self.conf_var.get()
                results = self.model(self.original_image, conf=conf)
                result = results[0]
                
                # Process results
                self.root.after(0, lambda: self.process_results(result))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror(
                    "Error", f"Analysis failed: {str(e)}"))
            finally:
                self.root.after(0, lambda: self.analyze_btn.config(
                    state='normal', text='🔍 Analyze Image'))
        
        threading.Thread(target=analyze, daemon=True).start()
    
    def process_results(self, result):
        """Xử lý và hiển thị kết quả"""
        # Copy original image for drawing
        annotated_image = self.original_image.copy()
        
        detections = []
        results_text = f"🔍 ANALYSIS RESULTS\n"
        results_text += f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        results_text += f"Confidence threshold: {self.conf_var.get():.2f}\n"
        results_text += "=" * 40 + "\n"
        
        if len(result.boxes) > 0:
            results_text += f"🎯 {len(result.boxes)} detection(s) found:\n\n"
            
            for i, box in enumerate(result.boxes):
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                class_name = self.model.names[class_id]
                coords = box.xyxy[0].cpu().numpy().astype(int)
                
                # Store detection info
                detection = {
                    'class_name': class_name,
                    'confidence': confidence,
                    'coords': coords
                }
                detections.append(detection)
                
                # Draw bounding box
                self.draw_bbox(annotated_image, detection)
                
                # Add to results text
                results_text += f"{i+1}. {class_name}\n"
                results_text += f"   Confidence: {confidence:.2%}\n"
                results_text += f"   Location: ({coords[0]}, {coords[1]}) to ({coords[2]}, {coords[3]})\n"
                results_text += f"   Risk level: {self.assess_risk(confidence)}\n\n"
            
            # Summary
            results_text += self.generate_summary(detections)
            
        else:
            results_text += "✅ No lung cancer detected\n"
            results_text += "The scan appears normal.\n\n"
        
        # Display annotated image
        rgb_annotated = cv2.cvtColor(annotated_image, cv2.COLOR_BGR2RGB)
        self.display_image(rgb_annotated)
        
        # Update results text
        self.update_results_text(results_text)
    
    def draw_bbox(self, image, detection):
        """Vẽ bounding box lên ảnh"""
        class_name = detection['class_name']
        confidence = detection['confidence']
        coords = detection['coords']
        
        # Get color for this class
        color = self.colors.get(class_name, (255, 255, 255))
        
        # Draw rectangle
        cv2.rectangle(image, (coords[0], coords[1]), (coords[2], coords[3]), color, 3)
        
        # Prepare label text
        label_parts = []
        if self.show_names_var.get():
            label_parts.append(class_name)
        if self.show_conf_var.get():
            label_parts.append(f"{confidence:.2%}")
        
        if label_parts:
            label = " | ".join(label_parts)
            
            # Calculate text size
            (text_width, text_height), baseline = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)
            
            # Draw background for text
            cv2.rectangle(image, 
                         (coords[0], coords[1] - text_height - 10),
                         (coords[0] + text_width, coords[1]),
                         color, -1)
            
            # Draw text
            cv2.putText(image, label, (coords[0], coords[1] - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    def assess_risk(self, confidence):
        """Đánh giá mức độ rủi ro"""
        if confidence >= 0.8:
            return "🔴 HIGH - Immediate attention required"
        elif confidence >= 0.6:
            return "🟡 MEDIUM - Further examination recommended"
        elif confidence >= 0.4:
            return "🟢 LOW - Monitor and follow up"
        else:
            return "⚪ VERY LOW - Uncertain detection"
    
    def generate_summary(self, detections):
        """Tạo tóm tắt kết quả"""
        summary = "📋 SUMMARY:\n"
        summary += "-" * 20 + "\n"
        
        # Count by cancer type
        type_counts = {}
        high_risk = 0
        
        for det in detections:
            class_name = det['class_name']
            confidence = det['confidence']
            
            if class_name not in type_counts:
                type_counts[class_name] = []
            type_counts[class_name].append(confidence)
            
            if confidence >= 0.6:
                high_risk += 1
        
        # Display counts
        for cancer_type, confidences in type_counts.items():
            avg_conf = sum(confidences) / len(confidences)
            summary += f"• {cancer_type}: {len(confidences)} region(s)\n"
            summary += f"  Average confidence: {avg_conf:.2%}\n"
        
        summary += f"\n🚨 High-risk detections: {high_risk}\n"
        
        # Recommendation
        summary += "\n💡 RECOMMENDATION:\n"
        if high_risk > 0:
            summary += "⚠️  Immediate medical consultation required!\n"
        elif len(detections) > 0:
            summary += "📋 Schedule follow-up examination\n"
        else:
            summary += "✅ Regular monitoring recommended\n"
        
        return summary
    
    def update_results_text(self, text):
        """Cập nhật text box kết quả"""
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(1.0, text)
    
    def clear_results(self):
        """Xóa kết quả"""
        self.results_text.delete(1.0, tk.END)
        self.canvas.delete("all")
        self.canvas.create_text(400, 300, text="Select an image to begin analysis", 
                              font=('Arial', 16), fill='#bdc3c7')
        self.current_image = None
        self.original_image = None
        self.file_label.config(text="No file selected", fg='#7f8c8d')
        self.analyze_btn.config(state='disabled')

def main():
    """Main function"""
    root = tk.Tk()
    
    # Set window icon (if available)
    try:
        # You can add an icon file here
        # root.iconbitmap('icon.ico')
        pass
    except:
        pass
    
    app = LungCancerGUI(root)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1200 // 2)
    y = (root.winfo_screenheight() // 2) - (800 // 2)
    root.geometry(f"1200x800+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
