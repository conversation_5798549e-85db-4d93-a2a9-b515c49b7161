#!/usr/bin/env python3
"""
Lung Cancer Predictor using SVM Model (98% accuracy)
Dự đoán ung thư phổi sử dụng model SVM (độ chính xác 98%)

Based on: huongdan.md
Author: AI Assistant
Date: September 2, 2025
"""

import pandas as pd
import joblib
import numpy as np
from typing import Dict, List, Union
import os

class LungCancerPredictor98:
    """Class để dự đoán ung thư phổi sử dụng model SVM 98%"""
    
    def __init__(self, model_dir: str = "model"):
        """
        Khởi tạo predictor với model SVM 98%
        
        Args:
            model_dir (str): Thư mục chứa các file model
        """
        self.model_dir = model_dir
        self.model = None
        self.le_gender = None
        self.le_cancer = None
        
        # Danh sách các cột binary cần map từ {1: 0, 2: 1}
        self.binary_columns = [
            'SMOKING', 'YELLOW_FINGERS', 'ANXIETY', 'PEER_PRESSURE', 
            'CHRONIC DISEASE', 'FATIGUE ', 'ALLERGY ', 'WHEEZING', 
            'ALCOHOL CONSUMING', 'COUGHING', 'SHORTNESS OF BREATH', 
            'SWALLOWING DIFFICULTY', 'CHEST PAIN'
        ]
        
        # Danh sách tất cả các features theo thứ tự
        self.feature_columns = [
            'GENDER', 'AGE', 'SMOKING', 'YELLOW_FINGERS', 'ANXIETY',
            'PEER_PRESSURE', 'CHRONIC DISEASE', 'FATIGUE ', 'ALLERGY ',
            'WHEEZING', 'ALCOHOL CONSUMING', 'COUGHING',
            'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN'
        ]
        
        self.load_models()
    
    def load_models(self) -> bool:
        """
        Tải các file model và encoders
        
        Returns:
            bool: True nếu tải thành công, False nếu thất bại
        """
        try:
            # Đường dẫn các file model
            model_path = os.path.join(self.model_dir, 'svm_lung_cancer_98_model.pkl')
            gender_encoder_path = os.path.join(self.model_dir, 'le_gender.pkl')
            cancer_encoder_path = os.path.join(self.model_dir, 'le_cancer.pkl')
            
            # Kiểm tra file tồn tại
            for path, name in [(model_path, 'SVM Model'), 
                              (gender_encoder_path, 'Gender Encoder'), 
                              (cancer_encoder_path, 'Cancer Encoder')]:
                if not os.path.exists(path):
                    print(f"❌ {name} không tồn tại: {path}")
                    return False
            
            # Tải các model
            self.model = joblib.load(model_path)
            self.le_gender = joblib.load(gender_encoder_path)
            self.le_cancer = joblib.load(cancer_encoder_path)
            
            print("✅ Đã tải thành công:")
            print(f"   📊 SVM Model: {model_path}")
            print(f"   👤 Gender Encoder: {gender_encoder_path}")
            print(f"   🎯 Cancer Encoder: {cancer_encoder_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi tải model: {e}")
            return False
    
    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Tiền xử lý dữ liệu theo yêu cầu của model
        
        Args:
            df (pd.DataFrame): DataFrame chứa dữ liệu thô
            
        Returns:
            pd.DataFrame: DataFrame đã được tiền xử lý
        """
        # Tạo bản copy để không thay đổi dữ liệu gốc
        processed_df = df.copy()
        
        # Map các cột binary từ {1: 0, 2: 1}
        for col in self.binary_columns:
            if col in processed_df.columns:
                processed_df[col] = processed_df[col].map({1: 0, 2: 1})
                print(f"✅ Mapped {col}: {{1: 0, 2: 1}}")
        
        # Mã hóa cột GENDER
        if 'GENDER' in processed_df.columns:
            processed_df['GENDER'] = self.le_gender.transform(processed_df['GENDER'])
            print(f"✅ Encoded GENDER: {dict(zip(self.le_gender.classes_, self.le_gender.transform(self.le_gender.classes_)))}")
        
        return processed_df
    
    def predict_single(self, data: Dict) -> Dict:
        """
        Dự đoán cho một mẫu dữ liệu
        
        Args:
            data (Dict): Dictionary chứa dữ liệu của một bệnh nhân
            
        Returns:
            Dict: Kết quả dự đoán
        """
        if self.model is None:
            raise ValueError("Model chưa được tải. Vui lòng kiểm tra các file model.")
        
        try:
            # Chuyển đổi dict thành DataFrame
            df = pd.DataFrame([data])
            
            # Tiền xử lý
            processed_df = self.preprocess_data(df)
            
            # Lấy features theo đúng thứ tự
            X = processed_df[self.feature_columns]
            
            # Dự đoán
            y_pred = self.model.predict(X)
            
            # Chuyển đổi kết quả về nhãn gốc
            y_pred_label = self.le_cancer.inverse_transform(y_pred)[0]
            
            # Tính xác suất nếu model hỗ trợ
            probability = None
            if hasattr(self.model, 'predict_proba'):
                proba = self.model.predict_proba(X)[0]
                probability = {
                    'NO': float(proba[0]),
                    'YES': float(proba[1])
                }
            elif hasattr(self.model, 'decision_function'):
                decision_score = self.model.decision_function(X)[0]
                # Chuyển đổi decision score thành xác suất gần đúng
                prob_yes = 1 / (1 + np.exp(-decision_score))
                probability = {
                    'NO': float(1 - prob_yes),
                    'YES': float(prob_yes)
                }
            
            # Xác định mức độ rủi ro
            risk_level = "LOW"
            if probability and probability['YES'] > 0.7:
                risk_level = "HIGH"
            elif probability and probability['YES'] > 0.3:
                risk_level = "MEDIUM"
            
            result = {
                'prediction': y_pred_label,
                'prediction_numeric': int(y_pred[0]),
                'probability': probability,
                'risk_level': risk_level,
                'input_data': data
            }
            
            return result
            
        except Exception as e:
            raise Exception(f"Lỗi khi dự đoán: {e}")
    
    def predict_from_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Dự đoán cho nhiều mẫu từ DataFrame
        
        Args:
            df (pd.DataFrame): DataFrame chứa dữ liệu
            
        Returns:
            pd.DataFrame: DataFrame với kết quả dự đoán
        """
        if self.model is None:
            raise ValueError("Model chưa được tải.")
        
        # Tiền xử lý
        processed_df = self.preprocess_data(df)
        
        # Lấy features
        X = processed_df[self.feature_columns]
        
        # Dự đoán
        y_pred = self.model.predict(X)
        y_pred_labels = self.le_cancer.inverse_transform(y_pred)
        
        # Thêm kết quả vào DataFrame
        result_df = df.copy()
        result_df['PREDICTION'] = y_pred_labels
        result_df['PREDICTION_NUMERIC'] = y_pred
        
        # Thêm xác suất nếu có thể
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(X)
            result_df['PROB_NO'] = probabilities[:, 0]
            result_df['PROB_YES'] = probabilities[:, 1]
            result_df['RISK_LEVEL'] = result_df['PROB_YES'].apply(
                lambda x: 'HIGH' if x > 0.7 else 'MEDIUM' if x > 0.3 else 'LOW'
            )
        
        return result_df
    
    def create_sample_data(self) -> Dict:
        """
        Tạo dữ liệu mẫu để test
        
        Returns:
            Dict: Dữ liệu mẫu
        """
        sample_data = {
            'GENDER': 'M',
            'AGE': 65,
            'SMOKING': 2,
            'YELLOW_FINGERS': 1,
            'ANXIETY': 2,
            'PEER_PRESSURE': 1,
            'CHRONIC DISEASE': 2,
            'FATIGUE ': 2,
            'ALLERGY ': 1,
            'WHEEZING': 2,
            'ALCOHOL CONSUMING': 1,
            'COUGHING': 2,
            'SHORTNESS OF BREATH': 1,
            'SWALLOWING DIFFICULTY': 2,
            'CHEST PAIN': 2
        }
        
        return sample_data


def main():
    """Hàm main để test predictor"""
    print("🏥 LUNG CANCER PREDICTOR - SVM MODEL (98% ACCURACY)")
    print("=" * 60)
    
    # Khởi tạo predictor
    predictor = LungCancerPredictor98()
    
    if predictor.model is None:
        print("❌ Không thể tải model. Thoát chương trình.")
        return
    
    print("\n📋 THÔNG TIN MODEL:")
    print(f"   🎯 Độ chính xác: 98%")
    print(f"   📊 Loại model: SVM")
    print(f"   📈 Features: 15 đặc trưng")
    
    # Test với dữ liệu mẫu
    print("\n🧪 TEST VỚI DỮ LIỆU MẪU:")
    print("-" * 40)
    
    sample_data = predictor.create_sample_data()
    
    print("Dữ liệu đầu vào:")
    for key, value in sample_data.items():
        print(f"  {key}: {value}")
    
    try:
        result = predictor.predict_single(sample_data)
        
        print(f"\n📊 KẾT QUẢ DỰ ĐOÁN:")
        print(f"  🎯 Dự đoán: {result['prediction']}")
        print(f"  ⚠️ Mức độ rủi ro: {result['risk_level']}")
        
        if result['probability']:
            print(f"  📈 Xác suất:")
            print(f"     • Không ung thư: {result['probability']['NO']:.3f} ({result['probability']['NO']*100:.1f}%)")
            print(f"     • Ung thư: {result['probability']['YES']:.3f} ({result['probability']['YES']*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ Lỗi khi dự đoán: {e}")
    
    print("\n✅ Test hoàn thành!")
    print("\n💡 Cách sử dụng:")
    print("1. Import: from lung_cancer_predictor_98 import LungCancerPredictor98")
    print("2. Tạo instance: predictor = LungCancerPredictor98()")
    print("3. Dự đoán: result = predictor.predict_single(your_data)")


if __name__ == "__main__":
    main()
