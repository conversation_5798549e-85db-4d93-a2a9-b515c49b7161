#!/usr/bin/env python3
"""
Simple GUI for YOLO Lung Cancer Detection
Giao diện đơn giản cho nhận diện ung thư phổi
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import os

class SimpleYOLOGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🏥 Lung Cancer Detection - Simple GUI")
        self.root.geometry("800x600")
        
        self.model = None
        self.current_image = None
        self.setup_ui()
        self.load_model()
    
    def setup_ui(self):
        """Thiết lập giao diện đơn giản"""
        # Title
        title = tk.Label(self.root, text="🏥 Lung Cancer Detection", 
                        font=('Arial', 16, 'bold'), fg='#2c3e50')
        title.pack(pady=10)
        
        # Buttons frame
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(pady=10)
        
        # Select image button
        self.select_btn = tk.Button(btn_frame, text="📁 Select CT Scan", 
                                  command=self.select_image, font=('Arial', 12),
                                  bg='#3498db', fg='white', padx=20, pady=5)
        self.select_btn.pack(side='left', padx=5)
        
        # Analyze button
        self.analyze_btn = tk.Button(btn_frame, text="🔍 Analyze", 
                                   command=self.analyze_image, font=('Arial', 12),
                                   bg='#e74c3c', fg='white', padx=20, pady=5,
                                   state='disabled')
        self.analyze_btn.pack(side='left', padx=5)
        
        # Clear button
        self.clear_btn = tk.Button(btn_frame, text="🗑️ Clear", 
                                 command=self.clear_image, font=('Arial', 12),
                                 bg='#95a5a6', fg='white', padx=20, pady=5)
        self.clear_btn.pack(side='left', padx=5)
        
        # Status label
        self.status_label = tk.Label(self.root, text="🔄 Loading model...", 
                                   font=('Arial', 10), fg='#7f8c8d')
        self.status_label.pack(pady=5)
        
        # Image canvas
        canvas_frame = tk.Frame(self.root, relief='sunken', bd=2)
        canvas_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.canvas = tk.Canvas(canvas_frame, bg='#f8f9fa')
        self.canvas.pack(fill='both', expand=True)
        
        # Placeholder text
        self.canvas.create_text(400, 200, text="Select an image to begin", 
                              font=('Arial', 14), fill='#bdc3c7')
        
        # Results frame
        results_frame = tk.LabelFrame(self.root, text="📋 Results", font=('Arial', 11, 'bold'))
        results_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        self.results_label = tk.Label(results_frame, text="No analysis yet", 
                                    font=('Arial', 10), justify='left',
                                    wraplength=700)
        self.results_label.pack(padx=10, pady=10)
    
    def load_model(self):
        """Tải model"""
        try:
            from ultralytics import YOLO
            model_path = "best.pt"
            
            if os.path.exists(model_path):
                self.model = YOLO(model_path)
                self.status_label.config(text="✅ Model loaded successfully", fg='#27ae60')
            else:
                self.status_label.config(text="❌ Model file not found", fg='#e74c3c')
                messagebox.showerror("Error", f"Model file not found: {model_path}")
        except ImportError:
            self.status_label.config(text="❌ YOLO library not found", fg='#e74c3c')
            messagebox.showerror("Error", "Please install ultralytics: pip install ultralytics")
        except Exception as e:
            self.status_label.config(text="❌ Model loading failed", fg='#e74c3c')
            messagebox.showerror("Error", f"Failed to load model: {str(e)}")
    
    def select_image(self):
        """Chọn ảnh"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Select CT Scan Image",
            filetypes=file_types
        )
        
        if filename:
            self.load_image(filename)
    
    def load_image(self, filepath):
        """Tải và hiển thị ảnh"""
        try:
            # Load with OpenCV
            image = cv2.imread(filepath)
            if image is None:
                messagebox.showerror("Error", "Cannot load image")
                return
            
            self.current_image = image
            
            # Convert for display
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(rgb_image)
            
            # Resize to fit canvas
            canvas_width = 700
            canvas_height = 400
            
            img_width, img_height = pil_image.size
            scale = min(canvas_width/img_width, canvas_height/img_height, 1.0)
            
            if scale < 1.0:
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Display
            self.photo = ImageTk.PhotoImage(pil_image)
            self.canvas.delete("all")
            
            # Center image
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            x = max(10, (canvas_width - pil_image.width) // 2)
            y = max(10, (canvas_height - pil_image.height) // 2)
            
            self.canvas.create_image(x, y, anchor='nw', image=self.photo)
            
            # Update UI
            self.analyze_btn.config(state='normal')
            self.status_label.config(text=f"📁 Loaded: {os.path.basename(filepath)}", fg='#2c3e50')
            self.results_label.config(text="Image loaded. Click 'Analyze' to detect cancer.")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
    
    def analyze_image(self):
        """Phân tích ảnh"""
        if self.model is None:
            messagebox.showerror("Error", "Model not loaded")
            return
        
        if self.current_image is None:
            messagebox.showerror("Error", "No image loaded")
            return
        
        try:
            self.status_label.config(text="🔄 Analyzing...", fg='#f39c12')
            self.root.update()
            
            # Run detection
            results = self.model(self.current_image, conf=0.25)
            result = results[0]
            
            # Process results
            if len(result.boxes) > 0:
                # Draw bounding boxes
                annotated_image = self.draw_boxes(self.current_image.copy(), result)
                
                # Display annotated image
                self.display_annotated_image(annotated_image)
                
                # Show results
                results_text = f"🎯 Found {len(result.boxes)} detection(s):\n\n"
                
                for i, box in enumerate(result.boxes):
                    class_id = int(box.cls[0])
                    confidence = float(box.conf[0])
                    class_name = self.model.names[class_id]
                    
                    results_text += f"{i+1}. {class_name} ({confidence:.1%})\n"
                
                self.results_label.config(text=results_text)
                self.status_label.config(text="✅ Analysis complete", fg='#27ae60')
                
            else:
                self.results_label.config(text="✅ No cancer detected\nThe scan appears normal.")
                self.status_label.config(text="✅ Analysis complete - No detections", fg='#27ae60')
            
        except Exception as e:
            messagebox.showerror("Error", f"Analysis failed: {str(e)}")
            self.status_label.config(text="❌ Analysis failed", fg='#e74c3c')
    
    def draw_boxes(self, image, result):
        """Vẽ bounding boxes"""
        colors = {
            "Adenocarcinoma": (255, 0, 0),
            "Small Cell Carcinoma": (0, 255, 0),
            "Large Cell Carcinoma": (0, 0, 255),
            "Squamous Cell Carcinoma": (255, 255, 0)
        }
        
        for box in result.boxes:
            class_id = int(box.cls[0])
            confidence = float(box.conf[0])
            class_name = self.model.names[class_id]
            coords = box.xyxy[0].cpu().numpy().astype(int)
            
            # Get color
            color = colors.get(class_name, (255, 255, 255))
            
            # Draw rectangle
            cv2.rectangle(image, (coords[0], coords[1]), (coords[2], coords[3]), color, 3)
            
            # Draw label
            label = f"{class_name} {confidence:.1%}"
            (text_width, text_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)
            
            # Background for text
            cv2.rectangle(image, 
                         (coords[0], coords[1] - text_height - 10),
                         (coords[0] + text_width, coords[1]),
                         color, -1)
            
            # Text
            cv2.putText(image, label, (coords[0], coords[1] - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        return image
    
    def display_annotated_image(self, image):
        """Hiển thị ảnh đã được đánh dấu"""
        # Convert for display
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(rgb_image)
        
        # Resize to fit canvas
        canvas_width = 700
        canvas_height = 400
        
        img_width, img_height = pil_image.size
        scale = min(canvas_width/img_width, canvas_height/img_height, 1.0)
        
        if scale < 1.0:
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Display
        self.photo = ImageTk.PhotoImage(pil_image)
        self.canvas.delete("all")
        
        # Center image
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        x = max(10, (canvas_width - pil_image.width) // 2)
        y = max(10, (canvas_height - pil_image.height) // 2)
        
        self.canvas.create_image(x, y, anchor='nw', image=self.photo)
    
    def clear_image(self):
        """Xóa ảnh và kết quả"""
        self.canvas.delete("all")
        self.canvas.create_text(400, 200, text="Select an image to begin", 
                              font=('Arial', 14), fill='#bdc3c7')
        
        self.current_image = None
        self.analyze_btn.config(state='disabled')
        self.results_label.config(text="No analysis yet")
        self.status_label.config(text="Ready for new image", fg='#7f8c8d')

def main():
    """Main function"""
    try:
        root = tk.Tk()
        
        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (800 // 2)
        y = (root.winfo_screenheight() // 2) - (600 // 2)
        root.geometry(f"800x600+{x}+{y}")
        
        app = SimpleYOLOGUI(root)
        root.mainloop()
        
    except Exception as e:
        print(f"Failed to start GUI: {e}")
        messagebox.showerror("Error", f"Failed to start GUI: {e}")

if __name__ == "__main__":
    main()
