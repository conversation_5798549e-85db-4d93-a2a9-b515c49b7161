#!/usr/bin/env python3
"""
Train SVC model with real lung cancer survey data
Train model SVC với dữ liệu khảo sát ung thư phổi thực tế

Author: AI Assistant
Date: September 2, 2025
"""

import pandas as pd
import numpy as np
import joblib
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

def load_and_preprocess_data(file_path: str):
    """
    Tải và tiền xử lý dữ liệu
    
    Args:
        file_path (str): Đường dẫn đến file CSV
    
    Returns:
        tuple: (X, y, feature_names)
    """
    print("📊 Đang tải dữ liệu...")
    
    # Đ<PERSON>c dữ liệu
    df = pd.read_csv(file_path)
    print(f"✅ Đã tải {len(df)} mẫu dữ liệu")
    
    # Hiển thị thông tin cơ bản
    print(f"📋 Kích thước dữ liệu: {df.shape}")
    print(f"📋 Các cột: {list(df.columns)}")
    
    # Kiểm tra dữ liệu thiếu
    missing_data = df.isnull().sum()
    if missing_data.sum() > 0:
        print("⚠️ Dữ liệu thiếu:")
        print(missing_data[missing_data > 0])
    else:
        print("✅ Không có dữ liệu thiếu")
    
    # Xử lý cột GENDER
    if 'GENDER' in df.columns:
        le_gender = LabelEncoder()
        df['GENDER'] = le_gender.fit_transform(df['GENDER'])
        print(f"✅ Đã mã hóa GENDER: {dict(zip(le_gender.classes_, le_gender.transform(le_gender.classes_)))}")
    
    # Xử lý cột target LUNG_CANCER
    if 'LUNG_CANCER' in df.columns:
        le_target = LabelEncoder()
        df['LUNG_CANCER'] = le_target.fit_transform(df['LUNG_CANCER'])
        print(f"✅ Đã mã hóa LUNG_CANCER: {dict(zip(le_target.classes_, le_target.transform(le_target.classes_)))}")
    
    # Tách features và target
    feature_columns = [col for col in df.columns if col != 'LUNG_CANCER']
    X = df[feature_columns].values
    y = df['LUNG_CANCER'].values
    
    print(f"📈 Phân bố target:")
    unique, counts = np.unique(y, return_counts=True)
    for val, count in zip(unique, counts):
        label = "YES" if val == 1 else "NO"
        print(f"  {label}: {count} ({count/len(y)*100:.1f}%)")
    
    return X, y, feature_columns

def train_svc_model(X, y, feature_names):
    """
    Train SVC model với Grid Search
    
    Args:
        X: Features
        y: Target
        feature_names: Tên các features
    
    Returns:
        tuple: (best_model, scaler, results)
    """
    print("\n🤖 Bắt đầu training SVC model...")
    
    # Chia dữ liệu
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"📊 Training set: {len(X_train)} mẫu")
    print(f"📊 Test set: {len(X_test)} mẫu")
    
    # Chuẩn hóa dữ liệu
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Định nghĩa parameter grid cho Grid Search
    param_grid = {
        'C': [0.1, 1, 10, 100],
        'kernel': ['linear', 'rbf', 'poly'],
        'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
    }
    
    print("🔍 Đang thực hiện Grid Search...")
    
    # Grid Search với Cross Validation
    svc = SVC(probability=True, random_state=42)
    grid_search = GridSearchCV(
        svc, param_grid, cv=5, scoring='accuracy', 
        n_jobs=-1, verbose=1
    )
    
    grid_search.fit(X_train_scaled, y_train)
    
    # Lấy model tốt nhất
    best_model = grid_search.best_estimator_
    
    print(f"✅ Best parameters: {grid_search.best_params_}")
    print(f"✅ Best CV score: {grid_search.best_score_:.4f}")
    
    # Đánh giá trên test set
    y_pred = best_model.predict(X_test_scaled)
    y_pred_proba = best_model.predict_proba(X_test_scaled)
    
    # Tính các metrics
    accuracy = accuracy_score(y_test, y_pred)
    auc_score = roc_auc_score(y_test, y_pred_proba[:, 1])
    
    print(f"\n📊 KẾT QUẢ ĐÁNH GIÁ:")
    print(f"✅ Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"✅ AUC Score: {auc_score:.4f} ({auc_score*100:.2f}%)")
    
    # Classification report
    print(f"\n📋 Classification Report:")
    print(classification_report(y_test, y_pred, target_names=['NO', 'YES']))
    
    # Confusion Matrix
    print(f"\n📋 Confusion Matrix:")
    cm = confusion_matrix(y_test, y_pred)
    print(cm)
    
    results = {
        'accuracy': accuracy,
        'auc_score': auc_score,
        'best_params': grid_search.best_params_,
        'best_cv_score': grid_search.best_score_,
        'classification_report': classification_report(y_test, y_pred, target_names=['NO', 'YES'], output_dict=True),
        'confusion_matrix': cm.tolist(),
        'feature_names': feature_names
    }
    
    return best_model, scaler, results

def save_model(model, scaler, results, model_path="model/svc_lung_cancer_new.pkl"):
    """
    Lưu model và thông tin liên quan
    
    Args:
        model: Trained model
        scaler: Fitted scaler
        results: Training results
        model_path: Đường dẫn lưu model
    """
    print(f"\n💾 Đang lưu model...")
    
    # Tạo thư mục nếu chưa tồn tại
    import os
    os.makedirs(os.path.dirname(model_path), exist_ok=True)
    
    # Tạo package để lưu
    model_package = {
        'model': model,
        'scaler': scaler,
        'results': results,
        'feature_names': results['feature_names'],
        'model_type': 'SVC',
        'sklearn_version': joblib.__version__
    }
    
    # Lưu model
    joblib.dump(model_package, model_path)
    print(f"✅ Đã lưu model tại: {model_path}")
    
    # Test load model
    print("🔍 Đang test load model...")
    try:
        loaded_package = joblib.load(model_path)
        loaded_model = loaded_package['model']
        loaded_scaler = loaded_package['scaler']
        
        # Test prediction
        test_data = np.array([[1, 65, 2, 2, 1, 1, 2, 2, 1, 2, 2, 2, 2, 1, 2]])
        test_data_scaled = loaded_scaler.transform(test_data)
        pred = loaded_model.predict(test_data_scaled)
        pred_proba = loaded_model.predict_proba(test_data_scaled)
        
        print(f"✅ Model load test thành công!")
        print(f"📋 Test prediction: {pred[0]} (proba: {pred_proba[0][1]:.3f})")
        
    except Exception as e:
        print(f"❌ Lỗi khi test load model: {e}")

def main():
    """Hàm main"""
    print("🏥 TRAINING SVC MODEL FOR LUNG CANCER PREDICTION")
    print("=" * 60)
    
    # Đường dẫn file dữ liệu
    data_path = "data/survey lung cancer.csv"
    
    try:
        # Tải và tiền xử lý dữ liệu
        X, y, feature_names = load_and_preprocess_data(data_path)
        
        # Train model
        model, scaler, results = train_svc_model(X, y, feature_names)
        
        # Lưu model
        save_model(model, scaler, results)
        
        print("\n🎉 TRAINING HOÀN THÀNH!")
        print("=" * 60)
        print("💡 Cách sử dụng model:")
        print("1. Load model: package = joblib.load('model/svc_lung_cancer_new.pkl')")
        print("2. Lấy model: model = package['model']")
        print("3. Lấy scaler: scaler = package['scaler']")
        print("4. Dự đoán: pred = model.predict(scaler.transform(data))")
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình training: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
