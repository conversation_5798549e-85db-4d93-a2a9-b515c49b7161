#!/usr/bin/env python3
"""
Simple test script for lung cancer detection model
"""

import sys
import os

# Thêm đường dẫn để import
sys.path.append('/Users/<USER>/Documents/python-file-hoc/projects/test-yolo')

def test_model():
    """Test model cơ bản"""
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics imported successfully")
        
        # Kiểm tra model exists
        model_path = "/Users/<USER>/Documents/python-file-hoc/projects/test-yolo/best.pt"
        if os.path.exists(model_path):
            print(f"✅ Model found: {model_path}")
            
            # Load model
            model = YOLO(model_path)
            print("✅ Model loaded successfully")
            
            # Hiển thị thông tin model
            print(f"📋 Model classes: {list(model.names.values())}")
            print(f"📊 Number of classes: {len(model.names)}")
            
            return True
        else:
            print(f"❌ Model not found: {model_path}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def get_image_path():
    """Lấy đường dẫn ảnh từ người dùng"""
    image_path = input("📁 Nhập đường dẫn ảnh cần test (hoặc 'quit' để thoát): ").strip()
    
    if image_path.lower() == 'quit':
        return None
    
    if not os.path.exists(image_path):
        print(f"❌ Không tìm thấy file: {image_path}")
        return get_image_path()  # Hỏi lại
    
    return image_path

def test_detection(model_path, image_path, conf_threshold=0.25):
    """Test detection trên một ảnh"""
    try:
        from ultralytics import YOLO
        import cv2
        
        # Load model
        model = YOLO(model_path)
        
        print(f"🔍 Đang phân tích: {os.path.basename(image_path)}")
        
        # Run detection
        results = model(image_path, conf=conf_threshold)
        result = results[0]
        
        print(f"📊 Kết quả:")
        print(f"   Số objects phát hiện: {len(result.boxes)}")
        
        if len(result.boxes) > 0:
            print("   Chi tiết:")
            for i, box in enumerate(result.boxes):
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                class_name = model.names[class_id]
                
                print(f"   {i+1}. {class_name}: {confidence:.2%}")
        else:
            print("   ✅ Không phát hiện dấu hiệu bất thường")
        
        # Lưu kết quả
        annotated_image = result.plot()
        output_name = f"test_result_{os.path.basename(image_path)}"
        cv2.imwrite(output_name, annotated_image)
        print(f"💾 Đã lưu kết quả: {output_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi test detection: {e}")
        return False

def main():
    """Main function"""
    print("🏥 LUNG CANCER DETECTION - SIMPLE TEST")
    print("=" * 50)
    
    # Test model loading
    if not test_model():
        print("❌ Model test failed!")
        return
    
    print("\n" + "=" * 50)
    
    # Test detection
    model_path = "/Users/<USER>/Documents/python-file-hoc/projects/test-yolo/best.pt"
    
    while True:
        image_path = get_image_path()
        if image_path is None:
            break
        
        # Lấy confidence threshold
        conf_input = input("🎯 Confidence threshold (0.1-1.0, mặc định 0.25): ").strip()
        try:
            conf = float(conf_input) if conf_input else 0.25
            if not 0.1 <= conf <= 1.0:
                conf = 0.25
                print("⚠️  Sử dụng confidence mặc định: 0.25")
        except:
            conf = 0.25
            print("⚠️  Sử dụng confidence mặc định: 0.25")
        
        print("-" * 50)
        test_detection(model_path, image_path, conf)
        print("-" * 50)
        
        # Hỏi có muốn tiếp tục không
        continue_test = input("\n🔄 Test ảnh khác? (y/n): ").strip().lower()
        if continue_test not in ['y', 'yes']:
            break
    
    print("👋 Cảm ơn bạn đã sử dụng!")

if __name__ == "__main__":
    main()
