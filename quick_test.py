from ultralytics import YOLO
import cv2
import os

def quick_test():
    """Test nhanh model YOLO với ảnh"""
    
    # Đường dẫn model
    model_path = "best.pt"
    
    # Tải model
    print("🔄 Đang tải model...")
    model = YOLO(model_path)
    print(f"✅ Đã tải model: {model_path}")
    print(f"📋 Classes: {list(model.names.values())}")
    
    # Nhập đường dẫn ảnh
    image_path = input("\n📁 Nhập đường dẫn ảnh cần test: ").strip()
    
    if not os.path.exists(image_path):
        print("❌ Không tìm thấy file ảnh!")
        return
    
    # Confidence threshold
    conf = float(input("🎯 Nhập confidence threshold (0.1-1.0, mặc định 0.25): ") or "0.25")
    
    print(f"\n🔍 Đang phân tích ảnh: {os.path.basename(image_path)}")
    
    # Chạy detection
    results = model(image_path, conf=conf)
    result = results[0]
    
    # In kết quả
    print(f"\n📊 Kết quả:")
    print(f"   Số objects phát hiện: {len(result.boxes)}")
    
    if len(result.boxes) > 0:
        print("   Chi tiết:")
        for i, box in enumerate(result.boxes):
            class_id = int(box.cls[0])
            confidence = float(box.conf[0])
            class_name = model.names[class_id]
            
            print(f"   {i+1}. {class_name}: {confidence:.2f}")
    else:
        print("   Không phát hiện object nào")
    
    # Vẽ và lưu kết quả
    annotated_image = result.plot()
    output_name = f"result_{os.path.basename(image_path)}"
    cv2.imwrite(output_name, annotated_image)
    
    print(f"\n💾 Đã lưu kết quả: {output_name}")
    
    # Hiển thị ảnh (nếu có GUI)
    try:
        cv2.imshow('YOLO Detection Result', annotated_image)
        print("👀 Nhấn phím bất kỳ để đóng cửa sổ hiển thị...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    except:
        print("ℹ️  Không thể hiển thị ảnh (không có GUI)")

if __name__ == "__main__":
    quick_test()
