#!/usr/bin/env python3
"""
Final system test for AI Doctor + SVM 98% model
Test cuối cùng cho hệ thống AI Doctor + model SVM 98%

Author: AI Assistant
Date: September 2, 2025
"""

import requests
import json
import time

def test_system_health():
    """Test system health and model status"""
    print("🏥 TESTING SYSTEM HEALTH")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ System Status: HEALTHY")
            print(f"   Version: {result.get('version', 'N/A')}")
            
            models = result.get('models', {})
            print("📊 Model Status:")
            
            # Check each model
            survey_ok = models.get('survey_model', False)
            yolo_ok = models.get('yolo_model', False)
            gemini_ok = models.get('gemini_ai', False)
            
            print(f"   • SVM Survey Model (98%): {'✅ LOADED' if survey_ok else '❌ NOT LOADED'}")
            print(f"   • YOLO Image Model: {'✅ LOADED' if yolo_ok else '❌ NOT LOADED'}")
            print(f"   • Gemini AI Doctor: {'✅ AVAILABLE' if gemini_ok else '⚠️ NEEDS API KEY'}")
            
            return survey_ok, yolo_ok, gemini_ok
            
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False, False, False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend")
        print("   Please start backend: python app.py")
        return False, False, False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False, False, False

def test_survey_prediction():
    """Test survey prediction with model confidence"""
    print("\n📊 TESTING SURVEY PREDICTION (SVM 98%)")
    print("=" * 50)
    
    # High-risk patient data
    high_risk_data = {
        'gender': 1,  # Male
        'age': 70,
        'smoking': 1,  # Yes
        'yellow_fingers': 1,  # Yes
        'anxiety': 1,  # Yes
        'peer_pressure': 0,  # No
        'chronic_disease': 1,  # Yes
        'fatigue': 1,  # Yes
        'allergy': 0,  # No
        'wheezing': 1,  # Yes
        'alcohol_consuming': 1,  # Yes
        'coughing': 1,  # Yes
        'shortness_of_breath': 1,  # Yes
        'swallowing_difficulty': 1,  # Yes
        'chest_pain': 1  # Yes
    }
    
    # Low-risk patient data
    low_risk_data = {
        'gender': 0,  # Female
        'age': 25,
        'smoking': 0,  # No
        'yellow_fingers': 0,  # No
        'anxiety': 0,  # No
        'peer_pressure': 0,  # No
        'chronic_disease': 0,  # No
        'fatigue': 0,  # No
        'allergy': 0,  # No
        'wheezing': 0,  # No
        'alcohol_consuming': 0,  # No
        'coughing': 0,  # No
        'shortness_of_breath': 0,  # No
        'swallowing_difficulty': 0,  # No
        'chest_pain': 0  # No
    }
    
    test_cases = [
        ("High Risk Patient", high_risk_data),
        ("Low Risk Patient", low_risk_data)
    ]
    
    results = []
    
    for case_name, data in test_cases:
        print(f"\n🧪 Testing {case_name}:")
        
        try:
            response = requests.post("http://localhost:8000/predict/survey", data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                
                prediction = result.get('prediction', 'N/A')
                risk_level = result.get('risk_level', 'N/A')
                model_confidence = result.get('model_confidence', 'N/A')
                model_accuracy = result.get('model_accuracy', 'N/A')
                
                confidence = result.get('confidence', {})
                cancer_prob = confidence.get('cancer', 0) * 100
                
                print(f"   📊 Prediction: {prediction}")
                print(f"   ⚠️ Risk Level: {risk_level}")
                print(f"   🔒 Model Confidence: {model_confidence}")
                print(f"   🎯 Model Accuracy: {model_accuracy}")
                print(f"   📈 Cancer Probability: {cancer_prob:.1f}%")
                
                # Check if medical advice is available
                if result.get('medical_advice'):
                    print(f"   🤖 AI Doctor: ✅ ADVICE PROVIDED")
                else:
                    print(f"   🤖 AI Doctor: ⚠️ NEEDS API KEY")
                
                results.append({
                    'case': case_name,
                    'prediction': prediction,
                    'risk_level': risk_level,
                    'confidence': model_confidence,
                    'success': True
                })
                
            else:
                print(f"   ❌ API Error: {response.status_code}")
                results.append({'case': case_name, 'success': False})
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({'case': case_name, 'success': False})
    
    return results

def test_api_endpoints():
    """Test all API endpoints"""
    print("\n🔧 TESTING API ENDPOINTS")
    print("=" * 30)
    
    endpoints = [
        ("GET", "/", "Main page"),
        ("GET", "/health", "Health check"),
        ("GET", "/docs", "API documentation")
    ]
    
    for method, endpoint, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ {description}: OK")
            else:
                print(f"   ⚠️ {description}: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {description}: Error - {e}")

def main():
    """Main test function"""
    print("🤖 FINAL SYSTEM TEST - AI DOCTOR + SVM 98%")
    print("=" * 60)
    print(f"🕒 Test started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test system health
    survey_ok, yolo_ok, gemini_ok = test_system_health()
    
    if not survey_ok:
        print("\n❌ CRITICAL: Survey model not loaded!")
        print("   Cannot proceed with tests.")
        return
    
    # Test API endpoints
    test_api_endpoints()
    
    # Test survey predictions
    survey_results = test_survey_prediction()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 60)
    
    print(f"🏥 System Health:")
    print(f"   • Backend: {'✅ RUNNING' if survey_ok else '❌ DOWN'}")
    print(f"   • SVM Model (98%): {'✅ LOADED' if survey_ok else '❌ NOT LOADED'}")
    print(f"   • YOLO Model: {'✅ LOADED' if yolo_ok else '❌ NOT LOADED'}")
    print(f"   • Gemini AI: {'✅ AVAILABLE' if gemini_ok else '⚠️ NEEDS API KEY'}")
    
    print(f"\n📊 Survey Predictions:")
    successful_tests = sum(1 for r in survey_results if r.get('success', False))
    total_tests = len(survey_results)
    print(f"   • Success Rate: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.0f}%)")
    
    for result in survey_results:
        if result.get('success'):
            case = result['case']
            pred = result['prediction']
            risk = result['risk_level']
            conf = result['confidence']
            print(f"   • {case}: {pred} ({risk} risk, {conf} confidence)")
    
    # Overall status
    overall_success = survey_ok and successful_tests == total_tests
    
    print(f"\n🎯 OVERALL STATUS: {'✅ SYSTEM WORKING' if overall_success else '⚠️ PARTIAL FUNCTIONALITY'}")
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ System ready for production use")
        
        print("\n💡 HOW TO USE:")
        print("1. Web Interface: http://localhost:8000")
        print("2. Desktop GUI: python gui.py")
        print("3. API Documentation: http://localhost:8000/docs")
        
        if not gemini_ok:
            print("\n🤖 TO ENABLE AI DOCTOR:")
            print("1. Get API key: https://makersuite.google.com/app/apikey")
            print("2. Add to .env: GEMINI_API_KEY=your_key")
            print("3. Restart: python app.py")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Please check the errors above and fix them.")
    
    print(f"\n🕒 Test completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
