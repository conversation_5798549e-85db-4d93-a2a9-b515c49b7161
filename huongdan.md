# HƯỚNG DẪN CHO AI VIẾT CODE SỬ DỤNG MÔ HÌNH DỰ ĐOÁN UNG THƯ PHỔI

## Thông tin chung
- **<PERSON><PERSON><PERSON> đích**: Hướng dẫn AI tạo code để tải và sử dụng mô hình SVM (độ chính xác 98%) để dự đoán nguy cơ ung thư phổi.
- **Ngày tạo**: 02/09/2025, 10:26 PM +07.
- **Tệp mô hình**:
  - `svm_lung_cancer_98_model.pkl`: Mô hình SVM đã huấn luyện bằng RandomizedSearchCV.
  - `le_gender.pkl`: Bộ mã hóa LabelEncoder cho cột GENDER (M/F).
  - `le_cancer.pkl`: Bộ mã hóa LabelEncoder cho cột LUNG_CANCER (YES/NO).
- **Nguồn dữ liệu**: Tập dữ liệu khảo sát ung thư phổi với 15 đặc trưng (GENDER, AGE, SMOKING, YELLOW_FINGERS, ANXIETY, PEER_PRESSURE, CHRONIC DISEASE, FATIGUE, ALLERGY, WHEEZING, ALCOHOL CONSUMING, COUGHING, SHORTNESS OF BREATH, SWALLOWING DIFFICULTY, CHEST PAIN) và nhãn LUNG_CANCER.

## Yêu cầu hệ thống
- **Ngôn ngữ lập trình**: Python 3.7 trở lên.
- **Thư viện cần thiết**:
  - `pandas`
  - `joblib`
  - `numpy` (tuỳ chọn, để xử lý dữ liệu số)
- **Cài đặt**:
  - Chạy lệnh: `pip install pandas joblib numpy`

## Cấu trúc dữ liệu đầu vào
- **Định dạng**: DataFrame pandas với 15 cột đặc trưng (không bao gồm LUNG_CANCER nếu chỉ dự đoán).
- **Giá trị**:
  - `GENDER`: 'M' hoặc 'F' (sẽ được mã hóa).
  - `AGE`: Số nguyên (tuổi).
  - Các cột binary (SMOKING, YELLOW_FINGERS, ...): 1 (NO) hoặc 2 (YES), cần map sang 0 (NO) và 1 (YES).
- **Ví dụ dữ liệu**:
  ```
  GENDER,AGE,SMOKING,YELLOW_FINGERS,ANXIETY,PEER_PRESSURE,CHRONIC DISEASE,FATIGUE ,ALLERGY ,WHEEZING,ALCOHOL CONSUMING,COUGHING,SHORTNESS OF BREATH,SWALLOWING DIFFICULTY,CHEST PAIN
  M,65,2,1,2,1,2,2,1,2,1,2,1,2,2
  ```

## Hướng dẫn tạo code
### Bước 1: Tải và khởi tạo
- Sử dụng `joblib.load()` để tải các file mô hình và encoder.
- Đường dẫn đến file phải được chỉ định (ví dụ: cùng thư mục với script hoặc đường dẫn tuyệt đối).

### Bước 2: Tiền xử lý dữ liệu mới
- Map các cột binary từ {1: 0, 2: 1}.
- Mã hóa cột `GENDER` bằng `le_gender.transform()`.
- Tách features (`X_new`) từ DataFrame, bỏ cột `LUNG_CANCER` nếu có.

### Bước 3: Dự đoán
- Sử dụng mô hình `rcv.predict(X_new)` để tạo dự đoán.
- Chuyển đổi dự đoán (0/1) về nhãn gốc ('YES'/'NO') bằng `le_cancer.inverse_transform()`.

### Bước 4: Đầu ra
- In kết quả dự đoán (ví dụ: "Dự đoán: YES").
- Nếu có nhãn thực tế, thêm đánh giá (confusion matrix, classification report) bằng `sklearn.metrics`.

## Mã code mẫu (dành cho AI tạo)
### Phần nhập thư viện
```python
import pandas as pd
import joblib
```

### Phần tải mô hình và dự đoán
```python
# Tải mô hình và encoders
rcv = joblib.load('svm_lung_cancer_98_model.pkl')
le_gender = joblib.load('le_gender.pkl')
le_cancer = joblib.load('le_cancer.pkl')

# Dữ liệu mới (thay bằng dữ liệu thực tế)
new_data = {
    'GENDER': ['M'],
    'AGE': [65],
    'SMOKING': [2],
    'YELLOW_FINGERS': [1],
    'ANXIETY': [2],
    'PEER_PRESSURE': [1],
    'CHRONIC DISEASE': [2],
    'FATIGUE ': [2],
    'ALLERGY ': [1],
    'WHEEZING': [2],
    'ALCOHOL CONSUMING': [1],
    'COUGHING': [2],
    'SHORTNESS OF BREATH': [1],
    'SWALLOWING DIFFICULTY': [2],
    'CHEST PAIN': [2]
}
new_df = pd.DataFrame(new_data)

# Tiền xử lý
binary_columns = ['SMOKING', 'YELLOW_FINGERS', 'ANXIETY', 'PEER_PRESSURE', 'CHRONIC DISEASE', 
                  'FATIGUE ', 'ALLERGY ', 'WHEEZING', 'ALCOHOL CONSUMING', 'COUGHING', 
                  'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN']
for col in binary_columns:
    new_df[col] = new_df[col].map({1: 0, 2: 1})

new_df['GENDER'] = le_gender.transform(new_df['GENDER'])

# Dự đoán
X_new = new_df
y_pred_new = rcv.predict(X_new)
y_pred_labels = le_cancer.inverse_transform(y_pred_new)
print("Dự đoán trên dữ liệu mới:", y_pred_labels[0])
```

## Lưu ý quan trọng
- **Tải cả 3 file**: Cần có `svm_lung_cancer_98_model.pkl`, `le_gender.pkl`, và `le_cancer.pkl` trong cùng thư mục với script, nếu không sẽ lỗi.
- **Đồng nhất dữ liệu**: Đảm bảo dữ liệu mới có cùng thứ tự và số lượng cột như khi huấn luyện.
- **Xử lý lỗi**: Nếu giá trị mới (ví dụ: GENDER không phải 'M'/'F') xuất hiện, cần thêm kiểm tra và xử lý ngoại lệ.
- **Môi trường**: Chạy trên Python với các thư viện đã cài đặt.

## Kết luận
AI được yêu cầu tạo code dựa trên hướng dẫn này để tự động hóa quá trình dự đoán ung thư phổi. Nếu có thắc mắc hoặc cần điều chỉnh, hãy liên hệ [tên bạn] để hỗ trợ.

# KẾT THÚC HƯỚNG DẪN