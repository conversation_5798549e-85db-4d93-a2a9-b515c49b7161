#!/bin/bash

echo "🏥 Lung Cancer Detection System"
echo "================================"

# Chuyển đến thư mục dự án
cd "$(dirname "$0")"

# Kiểm tra môi trường ảo
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Kích hoạt môi trường ảo
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Kiểm tra model file
if [ ! -f "best.pt" ]; then
    echo "❌ Model file 'best.pt' not found!"
    echo "Please copy your trained model to this directory."
    exit 1
fi

# Cài đặt dependencies nếu cần
echo "📋 Checking dependencies..."
python -c "import ultralytics, cv2, tkinter" 2>/dev/null || {
    echo "📦 Installing dependencies..."
    pip install -r requirements.txt
}

# Khởi chạy GUI
echo "🚀 Starting GUI..."
python simple_gui.py

echo "👋 Thank you for using Lung Cancer Detection System!"
