#!/usr/bin/env python3
"""
Lung Cancer Detection API - Unified Backend
API dự đoán ung thư phổi - Backend thống nhất

Author: AI Assistant
Date: September 2, 2025
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
import uvicorn
import os
import cv2
import numpy as np
import joblib
import pandas as pd
from PIL import Image
import io
import base64
from typing import List, Optional
import logging
from datetime import datetime
from ultralytics import YOLO
import warnings
warnings.filterwarnings('ignore')

# Gemini AI
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Lung Cancer Detection API",
    description="API tích hợp Survey Model và YOLO Detection",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
survey_model = None
le_gender = None
le_cancer = None
yolo_model = None
gemini_model = None

# Constants
SURVEY_FEATURES = [
    'GENDER', 'AGE', 'SMOKING', 'YELLOW_FINGERS', 'ANXIETY',
    'PEER_PRESSURE', 'CHRONIC DISEASE', 'FATIGUE ', 'ALLERGY ',
    'WHEEZING', 'ALCOHOL CONSUMING', 'COUGHING',
    'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN'
]

# Binary columns that need mapping from {1: 0, 2: 1}
BINARY_COLUMNS = [
    'SMOKING', 'YELLOW_FINGERS', 'ANXIETY', 'PEER_PRESSURE',
    'CHRONIC DISEASE', 'FATIGUE ', 'ALLERGY ', 'WHEEZING',
    'ALCOHOL CONSUMING', 'COUGHING', 'SHORTNESS OF BREATH',
    'SWALLOWING DIFFICULTY', 'CHEST PAIN'
]

CANCER_TYPES = {
    0: "Adenocarcinoma",
    1: "Large Cell Carcinoma", 
    2: "Small Cell Carcinoma",
    3: "Squamous Cell Carcinoma"
}

def load_models():
    """Load all models"""
    global survey_model, le_gender, le_cancer, yolo_model, gemini_model

    try:
        # Load SVM Model (98% accuracy)
        svm_model_path = "model/svm_lung_cancer_98_model.pkl"
        gender_encoder_path = "model/le_gender.pkl"
        cancer_encoder_path = "model/le_cancer.pkl"

        if os.path.exists(svm_model_path) and os.path.exists(gender_encoder_path) and os.path.exists(cancer_encoder_path):
            survey_model = joblib.load(svm_model_path)
            le_gender = joblib.load(gender_encoder_path)
            le_cancer = joblib.load(cancer_encoder_path)
            logger.info("✅ SVM Survey model (98% accuracy) loaded successfully")
        else:
            logger.warning("⚠️ SVM Survey model files not found")

        # Load YOLO Model
        yolo_model_path = "model/best.pt"
        if os.path.exists(yolo_model_path):
            yolo_model = YOLO(yolo_model_path)
            logger.info("✅ YOLO model loaded successfully")
        else:
            logger.warning("⚠️ YOLO model not found")

        # Configure Gemini AI
        if GEMINI_AVAILABLE:
            gemini_api_key = os.getenv("GEMINI_API_KEY")
            if gemini_api_key:
                genai.configure(api_key=gemini_api_key)
                gemini_model = genai.GenerativeModel('gemini-pro')
                logger.info("✅ Gemini AI configured successfully")
            else:
                logger.warning("⚠️ GEMINI_API_KEY not found in environment variables")
        else:
            logger.warning("⚠️ Gemini AI not available (google-generativeai not installed)")

    except Exception as e:
        logger.error(f"❌ Error loading models: {str(e)}")

# Load models on startup
load_models()

def get_medical_advice(survey_result: dict, image_result: dict = None) -> str:
    """
    Get medical advice from Gemini AI based on model predictions

    Args:
        survey_result: Result from SVM survey model
        image_result: Result from YOLO image model (optional)

    Returns:
        str: Medical advice from AI doctor
    """
    if not gemini_model:
        return "AI medical consultation not available. Please consult with a healthcare professional."

    try:
        # Prepare prompt for Gemini AI
        prompt = f"""
Bạn là một bác sĩ chuyên khoa ung thư phổi có kinh nghiệm 20 năm. Hãy đưa ra lời khuyên y tế dựa trên kết quả dự đoán từ các mô hình AI:

**KẾT QUẢ KHẢO SÁT (SVM Model - Độ chính xác 98%):**
- Dự đoán: {survey_result.get('prediction', 'N/A')}
- Mức độ rủi ro: {survey_result.get('risk_level', 'N/A')}
- Độ tin cậy: {survey_result.get('confidence', {}).get('cancer', 0)*100:.1f}% (ung thư)
- Độ chính xác model: {survey_result.get('model_accuracy', '98%')}
"""

        if image_result:
            prompt += f"""
**KẾT QUẢ PHÂN TÍCH HÌNH ẢNH CT (YOLO Model):**
- Trạng thái: {image_result.get('status', 'N/A')}
- Số vùng phát hiện: {image_result.get('total_detections', 0)}
"""
            if image_result.get('detections'):
                prompt += "- Các loại ung thư phát hiện:\n"
                for detection in image_result['detections']:
                    prompt += f"  + {detection['cancer_type']}: {detection['confidence']*100:.1f}% tin cậy\n"

        prompt += """
Hãy đưa ra:
1. **ĐÁNH GIÁ TỔNG QUAN** về tình trạng sức khỏe
2. **MỨC ĐỘ NGUY CƠ** (Thấp/Trung bình/Cao)
3. **KHUYẾN NGHỊ CỤ THỂ** về các bước tiếp theo
4. **LỜI KHUYÊN CHĂM SÓC** sức khỏe hàng ngày
5. **KHI NÀO CẦN ĐI KHÁM** gấp

Lưu ý: Đây chỉ là tham khảo từ AI, không thay thế cho chẩn đoán y tế chuyên nghiệp.
Trả lời bằng tiếng Việt, ngắn gọn và dễ hiểu.
"""

        # Get response from Gemini
        response = gemini_model.generate_content(prompt)
        return response.text

    except Exception as e:
        logger.error(f"Error getting medical advice: {e}")
        return f"Không thể tạo lời khuyên y tế. Vui lòng tham khảo ý kiến bác sĩ chuyên khoa. (Lỗi: {str(e)})"

@app.get("/", response_class=HTMLResponse)
async def root():
    """Main page with simple interface"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Lung Cancer Detection API</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
            .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
            .endpoint { background-color: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .method { font-weight: bold; color: #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏥 Lung Cancer Detection API</h1>
            
            <div class="status success">
                <strong>✅ API Status:</strong> Running
            </div>
            
            <div class="status """ + ("success" if survey_model else "warning") + """">
                <strong>📊 SVM Model (98%):</strong> """ + ("Loaded" if survey_model else "Not Available") + """
            </div>
            
            <div class="status """ + ("success" if yolo_model else "warning") + """">
                <strong>🔍 YOLO Model:</strong> """ + ("Loaded" if yolo_model else "Not Available") + """
            </div>

            <div class="status """ + ("success" if gemini_model else "warning") + """">
                <strong>🤖 Gemini AI Doctor:</strong> """ + ("Available" if gemini_model else "Not Available") + """
            </div>
            
            <h2>📋 Available Endpoints:</h2>
            
            <div class="endpoint">
                <div class="method">POST /predict/survey</div>
                <p>Dự đoán ung thư từ dữ liệu khảo sát (15 đặc trưng)</p>
            </div>
            
            <div class="endpoint">
                <div class="method">POST /predict/image</div>
                <p>Phát hiện ung thư từ ảnh CT scan</p>
            </div>
            
            <div class="endpoint">
                <div class="method">GET /docs</div>
                <p>Swagger UI documentation</p>
            </div>
            
            <div class="endpoint">
                <div class="method">POST /medical-advice</div>
                <p>Nhận lời khuyên y tế từ AI Doctor (Gemini)</p>
            </div>

            <div class="endpoint">
                <div class="method">GET /health</div>
                <p>Health check endpoint</p>
            </div>
            
            <p style="text-align: center; margin-top: 30px; color: #6c757d;">
                <strong>Version:</strong> 2.0.0 | <strong>Time:</strong> """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """
            </p>
        </div>
    </body>
    </html>
    """
    return html_content

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "models": {
            "survey_model": survey_model is not None,
            "yolo_model": yolo_model is not None,
            "gemini_ai": gemini_model is not None
        },
        "version": "2.0.0"
    }

@app.post("/predict/survey")
async def predict_survey(
    gender: int = Form(..., description="0: Female, 1: Male"),
    age: int = Form(..., description="Age in years"),
    smoking: int = Form(..., description="0: No, 1: Yes"),
    yellow_fingers: int = Form(..., description="0: No, 1: Yes"),
    anxiety: int = Form(..., description="0: No, 1: Yes"),
    peer_pressure: int = Form(..., description="0: No, 1: Yes"),
    chronic_disease: int = Form(..., description="0: No, 1: Yes"),
    fatigue: int = Form(..., description="0: No, 1: Yes"),
    allergy: int = Form(..., description="0: No, 1: Yes"),
    wheezing: int = Form(..., description="0: No, 1: Yes"),
    alcohol_consuming: int = Form(..., description="0: No, 1: Yes"),
    coughing: int = Form(..., description="0: No, 1: Yes"),
    shortness_of_breath: int = Form(..., description="0: No, 1: Yes"),
    swallowing_difficulty: int = Form(..., description="0: No, 1: Yes"),
    chest_pain: int = Form(..., description="0: No, 1: Yes")
):
    """Predict lung cancer from survey data"""
    if survey_model is None:
        raise HTTPException(status_code=503, detail="Survey model not loaded")

    try:
        # Prepare input data as DataFrame (following huongdan.md format)
        data = {
            'GENDER': 'M' if gender == 1 else 'F',
            'AGE': age,
            'SMOKING': 2 if smoking == 1 else 1,  # Convert 0/1 to 1/2 format
            'YELLOW_FINGERS': 2 if yellow_fingers == 1 else 1,
            'ANXIETY': 2 if anxiety == 1 else 1,
            'PEER_PRESSURE': 2 if peer_pressure == 1 else 1,
            'CHRONIC DISEASE': 2 if chronic_disease == 1 else 1,
            'FATIGUE ': 2 if fatigue == 1 else 1,
            'ALLERGY ': 2 if allergy == 1 else 1,
            'WHEEZING': 2 if wheezing == 1 else 1,
            'ALCOHOL CONSUMING': 2 if alcohol_consuming == 1 else 1,
            'COUGHING': 2 if coughing == 1 else 1,
            'SHORTNESS OF BREATH': 2 if shortness_of_breath == 1 else 1,
            'SWALLOWING DIFFICULTY': 2 if swallowing_difficulty == 1 else 1,
            'CHEST PAIN': 2 if chest_pain == 1 else 1
        }

        df = pd.DataFrame([data])

        # Preprocess data according to huongdan.md
        # Map binary columns from {1: 0, 2: 1}
        for col in BINARY_COLUMNS:
            if col in df.columns:
                df[col] = df[col].map({1: 0, 2: 1})

        # Encode GENDER
        df['GENDER'] = le_gender.transform(df['GENDER'])

        # Get features in correct order
        X = df[SURVEY_FEATURES]

        # Make prediction
        prediction = survey_model.predict(X)[0]

        # Convert prediction back to label
        prediction_label = le_cancer.inverse_transform([prediction])[0]

        # Get probabilities
        probabilities = None
        if hasattr(survey_model, 'predict_proba'):
            proba = survey_model.predict_proba(X)[0]
            probabilities = {
                "no_cancer": float(proba[0]),
                "cancer": float(proba[1])
            }
        elif hasattr(survey_model, 'decision_function'):
            decision_score = survey_model.decision_function(X)[0]
            prob_cancer = 1 / (1 + np.exp(-decision_score))
            probabilities = {
                "no_cancer": float(1 - prob_cancer),
                "cancer": float(prob_cancer)
            }

        # Determine risk level
        risk_level = "LOW"
        if probabilities:
            cancer_prob = probabilities['cancer']
            if cancer_prob > 0.7:
                risk_level = "HIGH"
            elif cancer_prob > 0.3:
                risk_level = "MEDIUM"

        # Calculate model confidence (how sure the model is about its prediction)
        model_confidence = "HIGH"
        if probabilities:
            max_prob = max(probabilities['no_cancer'], probabilities['cancer'])
            if max_prob < 0.6:
                model_confidence = "LOW"
            elif max_prob < 0.8:
                model_confidence = "MEDIUM"

        result = {
            "prediction": prediction_label,
            "confidence": probabilities,
            "risk_level": risk_level,
            "model_accuracy": "98%",
            "model_confidence": model_confidence,
            "timestamp": datetime.now().isoformat()
        }

        # Get medical advice from Gemini AI
        if gemini_model:
            try:
                medical_advice = get_medical_advice(result)
                result["medical_advice"] = medical_advice
            except Exception as e:
                logger.error(f"Error getting medical advice: {e}")
                result["medical_advice"] = "Không thể tạo lời khuyên y tế. Vui lòng tham khảo ý kiến bác sĩ chuyên khoa."

        return result

    except Exception as e:
        logger.error(f"Survey prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/predict/image")
async def predict_image(file: UploadFile = File(...)):
    """Detect cancer from CT scan image"""
    if yolo_model is None:
        raise HTTPException(status_code=503, detail="YOLO model not loaded")

    try:
        # Read and process image
        contents = await file.read()
        nparr = np.frombuffer(contents, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if image is None:
            raise HTTPException(status_code=400, detail="Invalid image format")

        # Run YOLO detection
        results = yolo_model(image, conf=0.25)
        result = results[0]

        detections = []
        if len(result.boxes) > 0:
            for box in result.boxes:
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                cancer_type = CANCER_TYPES.get(class_id, "Unknown")

                detections.append({
                    "cancer_type": cancer_type,
                    "confidence": confidence,
                    "bbox": box.xyxy[0].tolist()
                })

        response = {
            "detections": detections,
            "total_detections": len(detections),
            "status": "cancer_detected" if detections else "no_cancer_detected",
            "timestamp": datetime.now().isoformat()
        }

        return response

    except Exception as e:
        logger.error(f"Image prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")

@app.post("/medical-advice")
async def get_medical_consultation(
    # Survey data
    gender: int = Form(..., description="0: Female, 1: Male"),
    age: int = Form(..., description="Age in years"),
    smoking: int = Form(..., description="0: No, 1: Yes"),
    yellow_fingers: int = Form(..., description="0: No, 1: Yes"),
    anxiety: int = Form(..., description="0: No, 1: Yes"),
    peer_pressure: int = Form(..., description="0: No, 1: Yes"),
    chronic_disease: int = Form(..., description="0: No, 1: Yes"),
    fatigue: int = Form(..., description="0: No, 1: Yes"),
    allergy: int = Form(..., description="0: No, 1: Yes"),
    wheezing: int = Form(..., description="0: No, 1: Yes"),
    alcohol_consuming: int = Form(..., description="0: No, 1: Yes"),
    coughing: int = Form(..., description="0: No, 1: Yes"),
    shortness_of_breath: int = Form(..., description="0: No, 1: Yes"),
    swallowing_difficulty: int = Form(..., description="0: No, 1: Yes"),
    chest_pain: int = Form(..., description="0: No, 1: Yes"),
    # Optional image
    file: Optional[UploadFile] = File(None)
):
    """Get comprehensive medical advice from AI Doctor based on both models"""
    if not gemini_model:
        raise HTTPException(status_code=503, detail="Gemini AI not available")

    try:
        # Get survey prediction first
        survey_data = {
            'gender': gender, 'age': age, 'smoking': smoking, 'yellow_fingers': yellow_fingers,
            'anxiety': anxiety, 'peer_pressure': peer_pressure, 'chronic_disease': chronic_disease,
            'fatigue': fatigue, 'allergy': allergy, 'wheezing': wheezing,
            'alcohol_consuming': alcohol_consuming, 'coughing': coughing,
            'shortness_of_breath': shortness_of_breath, 'swallowing_difficulty': swallowing_difficulty,
            'chest_pain': chest_pain
        }

        # Process survey data (same logic as predict_survey)
        data = {
            'GENDER': 'M' if gender == 1 else 'F',
            'AGE': age,
            'SMOKING': 2 if smoking == 1 else 1,
            'YELLOW_FINGERS': 2 if yellow_fingers == 1 else 1,
            'ANXIETY': 2 if anxiety == 1 else 1,
            'PEER_PRESSURE': 2 if peer_pressure == 1 else 1,
            'CHRONIC DISEASE': 2 if chronic_disease == 1 else 1,
            'FATIGUE ': 2 if fatigue == 1 else 1,
            'ALLERGY ': 2 if allergy == 1 else 1,
            'WHEEZING': 2 if wheezing == 1 else 1,
            'ALCOHOL CONSUMING': 2 if alcohol_consuming == 1 else 1,
            'COUGHING': 2 if coughing == 1 else 1,
            'SHORTNESS OF BREATH': 2 if shortness_of_breath == 1 else 1,
            'SWALLOWING DIFFICULTY': 2 if swallowing_difficulty == 1 else 1,
            'CHEST PAIN': 2 if chest_pain == 1 else 1
        }

        df = pd.DataFrame([data])

        # Preprocess
        for col in BINARY_COLUMNS:
            if col in df.columns:
                df[col] = df[col].map({1: 0, 2: 1})

        df['GENDER'] = le_gender.transform(df['GENDER'])
        X = df[SURVEY_FEATURES]

        # Survey prediction
        prediction = survey_model.predict(X)[0]
        prediction_label = le_cancer.inverse_transform([prediction])[0]

        probabilities = None
        if hasattr(survey_model, 'predict_proba'):
            proba = survey_model.predict_proba(X)[0]
            probabilities = {
                "no_cancer": float(proba[0]),
                "cancer": float(proba[1])
            }
        elif hasattr(survey_model, 'decision_function'):
            decision_score = survey_model.decision_function(X)[0]
            prob_cancer = 1 / (1 + np.exp(-decision_score))
            probabilities = {
                "no_cancer": float(1 - prob_cancer),
                "cancer": float(prob_cancer)
            }

        risk_level = "LOW"
        if probabilities:
            cancer_prob = probabilities['cancer']
            if cancer_prob > 0.7:
                risk_level = "HIGH"
            elif cancer_prob > 0.3:
                risk_level = "MEDIUM"

        survey_result = {
            "prediction": prediction_label,
            "confidence": probabilities,
            "risk_level": risk_level,
            "model_accuracy": "98%"
        }

        # Image analysis (if provided)
        image_result = None
        if file and yolo_model:
            contents = await file.read()
            nparr = np.frombuffer(contents, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is not None:
                results = yolo_model(image, conf=0.25)
                result = results[0]

                detections = []
                if len(result.boxes) > 0:
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        cancer_type = CANCER_TYPES.get(class_id, "Unknown")

                        detections.append({
                            "cancer_type": cancer_type,
                            "confidence": confidence
                        })

                image_result = {
                    "detections": detections,
                    "total_detections": len(detections),
                    "status": "cancer_detected" if detections else "no_cancer_detected"
                }

        # Get comprehensive medical advice
        medical_advice = get_medical_advice(survey_result, image_result)

        response = {
            "survey_analysis": survey_result,
            "image_analysis": image_result,
            "medical_advice": medical_advice,
            "consultation_type": "comprehensive" if image_result else "survey_only",
            "timestamp": datetime.now().isoformat()
        }

        return response

    except Exception as e:
        logger.error(f"Medical consultation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Medical consultation failed: {str(e)}")

if __name__ == "__main__":
    print("🏥 Starting Lung Cancer Detection API...")
    print("=" * 50)
    print("📊 Loading models...")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
