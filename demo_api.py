#!/usr/bin/env python3
"""
Demo script to test SVM 98% model via API
Script demo để test model SVM 98% qua API

Author: AI Assistant
Date: September 2, 2025
"""

import requests
import json

def test_survey_api():
    """Test survey prediction API"""
    print("🧪 TESTING SURVEY API WITH SVM 98% MODEL")
    print("=" * 50)
    
    # API endpoint
    url = "http://localhost:8000/predict/survey"
    
    # Sample data (same as in huongdan.md)
    data = {
        'gender': 1,  # Male
        'age': 65,
        'smoking': 1,  # Yes (will be converted to format 2)
        'yellow_fingers': 0,  # No (will be converted to format 1)
        'anxiety': 1,  # Yes
        'peer_pressure': 0,  # No
        'chronic_disease': 1,  # Yes
        'fatigue': 1,  # Yes
        'allergy': 0,  # No
        'wheezing': 1,  # Yes
        'alcohol_consuming': 0,  # No
        'coughing': 1,  # Yes
        'shortness_of_breath': 0,  # No
        'swallowing_difficulty': 1,  # Yes
        'chest_pain': 1  # Yes
    }
    
    print("📋 Input data:")
    for key, value in data.items():
        print(f"   {key}: {value}")
    
    try:
        print("\n🚀 Making API request...")
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ API Response:")
            print(f"   🎯 Prediction: {result['prediction']}")
            print(f"   ⚠️ Risk Level: {result['risk_level']}")
            print(f"   📊 Model Accuracy: {result.get('model_accuracy', 'N/A')}")
            
            if result.get('confidence'):
                conf = result['confidence']
                print(f"   📈 Confidence:")
                print(f"      • No Cancer: {conf['no_cancer']:.3f} ({conf['no_cancer']*100:.1f}%)")
                print(f"      • Cancer: {conf['cancer']:.3f} ({conf['cancer']*100:.1f}%)")
            
            print(f"   🕒 Timestamp: {result['timestamp']}")
            
            return True
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Cannot connect to API")
        print("   Make sure the backend is running: python app.py")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_health_api():
    """Test health check API"""
    print("\n🏥 TESTING HEALTH CHECK API")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:8000/health")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Health Check:")
            print(f"   Status: {result['status']}")
            print(f"   Version: {result.get('version', 'N/A')}")
            print(f"   Models:")
            models = result.get('models', {})
            for model_name, loaded in models.items():
                status = "✅ Loaded" if loaded else "❌ Not Loaded"
                print(f"      • {model_name}: {status}")
            
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def main():
    """Main demo function"""
    print("🏥 SVM 98% MODEL API DEMO")
    print("=" * 60)
    
    # Test health check first
    health_ok = test_health_api()
    
    if health_ok:
        # Test survey prediction
        survey_ok = test_survey_api()
        
        print("\n" + "=" * 60)
        if survey_ok:
            print("🎉 ALL TESTS PASSED!")
            print("✅ SVM 98% model is working correctly via API")
            
            print("\n💡 How to use:")
            print("1. Web interface: http://localhost:8000")
            print("2. API docs: http://localhost:8000/docs")
            print("3. Desktop GUI: python gui.py")
            
        else:
            print("❌ SURVEY TEST FAILED!")
    else:
        print("❌ HEALTH CHECK FAILED!")
        print("   Make sure backend is running: python app.py")
    
    print("\n📋 Model Information:")
    print("   🎯 Accuracy: 98%")
    print("   📊 Type: SVM (RandomizedSearchCV)")
    print("   📈 Features: 15")
    print("   🔄 Preprocessing: Automatic (API handles it)")

if __name__ == "__main__":
    main()
