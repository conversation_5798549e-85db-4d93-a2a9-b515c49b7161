#!/usr/bin/env python3
"""
Demo script to test AI Doctor functionality with SVM 98% model
Script demo để test chức năng AI Doctor với model SVM 98%

Author: AI Assistant
Date: September 2, 2025
"""

import requests
import json

def test_enhanced_survey_api():
    """Test enhanced survey prediction API with model confidence"""
    print("🧪 TESTING ENHANCED SURVEY API (SVM 98% + Model Confidence)")
    print("=" * 70)
    
    # API endpoint
    url = "http://localhost:8000/predict/survey"
    
    # Sample data
    data = {
        'gender': 1,  # Male
        'age': 65,
        'smoking': 1,  # Yes
        'yellow_fingers': 0,  # No
        'anxiety': 1,  # Yes
        'peer_pressure': 0,  # No
        'chronic_disease': 1,  # Yes
        'fatigue': 1,  # Yes
        'allergy': 0,  # No
        'wheezing': 1,  # Yes
        'alcohol_consuming': 0,  # No
        'coughing': 1,  # Yes
        'shortness_of_breath': 0,  # No
        'swallowing_difficulty': 1,  # Yes
        'chest_pain': 1  # Yes
    }
    
    print("📋 Input data:")
    for key, value in data.items():
        print(f"   {key}: {value}")
    
    try:
        print("\n🚀 Making API request...")
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Enhanced API Response:")
            print(f"   🎯 Prediction: {result['prediction']}")
            print(f"   ⚠️ Risk Level: {result['risk_level']}")
            print(f"   📊 Model Accuracy: {result.get('model_accuracy', 'N/A')}")
            print(f"   🔒 Model Confidence: {result.get('model_confidence', 'N/A')}")
            
            if result.get('confidence'):
                conf = result['confidence']
                print(f"   📈 Prediction Confidence:")
                print(f"      • No Cancer: {conf['no_cancer']:.3f} ({conf['no_cancer']*100:.1f}%)")
                print(f"      • Cancer: {conf['cancer']:.3f} ({conf['cancer']*100:.1f}%)")
            
            # Check if medical advice is available
            if result.get('medical_advice'):
                print(f"\n🤖 AI DOCTOR ADVICE:")
                print("-" * 50)
                print(result['medical_advice'])
            else:
                print(f"\n⚠️ AI Doctor advice not available (Gemini API key needed)")
            
            print(f"\n🕒 Timestamp: {result['timestamp']}")
            
            return True
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Cannot connect to API")
        print("   Make sure the backend is running: python app.py")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_ai_doctor_consultation():
    """Test AI Doctor consultation endpoint"""
    print("\n🤖 TESTING AI DOCTOR CONSULTATION")
    print("=" * 50)
    
    # API endpoint
    url = "http://localhost:8000/medical-advice"
    
    # Sample data (same as survey)
    data = {
        'gender': 1,  # Male
        'age': 65,
        'smoking': 1,  # Yes
        'yellow_fingers': 0,  # No
        'anxiety': 1,  # Yes
        'peer_pressure': 0,  # No
        'chronic_disease': 1,  # Yes
        'fatigue': 1,  # Yes
        'allergy': 0,  # No
        'wheezing': 1,  # Yes
        'alcohol_consuming': 0,  # No
        'coughing': 1,  # Yes
        'shortness_of_breath': 0,  # No
        'swallowing_difficulty': 1,  # Yes
        'chest_pain': 1  # Yes
    }
    
    try:
        print("🚀 Requesting AI Doctor consultation...")
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ AI Doctor Consultation Response:")
            
            # Survey analysis
            if result.get('survey_analysis'):
                survey = result['survey_analysis']
                print(f"\n📊 Survey Analysis:")
                print(f"   • Prediction: {survey['prediction']}")
                print(f"   • Risk Level: {survey['risk_level']}")
                print(f"   • Model Accuracy: {survey['model_accuracy']}")
                if survey.get('confidence'):
                    conf = survey['confidence']
                    print(f"   • Cancer Probability: {conf['cancer']:.3f} ({conf['cancer']*100:.1f}%)")
            
            # Image analysis (if any)
            if result.get('image_analysis'):
                image = result['image_analysis']
                print(f"\n🔍 Image Analysis:")
                print(f"   • Status: {image['status']}")
                print(f"   • Detections: {image['total_detections']}")
            
            # Medical advice
            if result.get('medical_advice'):
                print(f"\n🩺 Medical Advice from AI Doctor:")
                print("-" * 40)
                print(result['medical_advice'])
            else:
                print(f"\n⚠️ Medical advice not available (Gemini API key needed)")
            
            print(f"\n📋 Consultation Type: {result.get('consultation_type', 'N/A')}")
            print(f"🕒 Timestamp: {result.get('timestamp', 'N/A')}")
            
            return True
            
        elif response.status_code == 503:
            print("⚠️ AI Doctor not available (Gemini API key needed)")
            print("   To enable AI Doctor:")
            print("   1. Get API key from: https://makersuite.google.com/app/apikey")
            print("   2. Create .env file: cp .env.example .env")
            print("   3. Add your key: GEMINI_API_KEY=your_key_here")
            print("   4. Restart backend: python app.py")
            return False
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_health_check():
    """Test health check with new AI Doctor status"""
    print("\n🏥 TESTING HEALTH CHECK (WITH AI DOCTOR STATUS)")
    print("-" * 50)
    
    try:
        response = requests.get("http://localhost:8000/health")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Health Check:")
            print(f"   Status: {result['status']}")
            print(f"   Version: {result.get('version', 'N/A')}")
            print(f"   Models:")
            models = result.get('models', {})
            for model_name, loaded in models.items():
                status = "✅ Available" if loaded else "❌ Not Available"
                print(f"      • {model_name.replace('_', ' ').title()}: {status}")
            
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def main():
    """Main demo function"""
    print("🤖 AI DOCTOR DEMO - SVM 98% MODEL + GEMINI AI")
    print("=" * 80)
    
    # Test health check first
    health_ok = test_health_check()
    
    if health_ok:
        # Test enhanced survey prediction
        survey_ok = test_enhanced_survey_api()
        
        # Test AI Doctor consultation
        doctor_ok = test_ai_doctor_consultation()
        
        print("\n" + "=" * 80)
        print("📋 DEMO SUMMARY:")
        print(f"   ✅ Health Check: {'PASSED' if health_ok else 'FAILED'}")
        print(f"   ✅ Enhanced Survey: {'PASSED' if survey_ok else 'FAILED'}")
        print(f"   🤖 AI Doctor: {'AVAILABLE' if doctor_ok else 'NEEDS API KEY'}")
        
        if survey_ok:
            print("\n🎉 CORE FUNCTIONALITY WORKING!")
            print("✅ SVM 98% model with enhanced features:")
            print("   • Model confidence levels")
            print("   • Detailed prediction probabilities")
            print("   • Risk level assessment")
            
            if not doctor_ok:
                print("\n💡 TO ENABLE AI DOCTOR:")
                print("1. Get Gemini API key: https://makersuite.google.com/app/apikey")
                print("2. Create .env file: cp .env.example .env")
                print("3. Add: GEMINI_API_KEY=your_key_here")
                print("4. Restart: python app.py")
        
        print("\n🎯 HOW TO USE:")
        print("1. Web interface: http://localhost:8000")
        print("2. API docs: http://localhost:8000/docs")
        print("3. Desktop GUI: python gui.py")
        
    else:
        print("❌ HEALTH CHECK FAILED!")
        print("   Make sure backend is running: python app.py")
    
    print("\n📊 ENHANCED FEATURES:")
    print("   🎯 Model Accuracy: 98% (SVM)")
    print("   🔒 Model Confidence: HIGH/MEDIUM/LOW")
    print("   📈 Prediction Probabilities: Detailed breakdown")
    print("   🤖 AI Doctor: Medical advice from Gemini AI")
    print("   🔍 Multi-modal: Survey + Image analysis")

if __name__ == "__main__":
    main()
