# 🏥 Lung Cancer Detection GUI

Giao diện người dùng cho hệ thống nhận diện ung thư phổi sử dụng YOLO model.

## 📋 Tính năng

### 🖥️ Giao diện chính (lung_cancer_gui.py)
- ✅ Giao diện đầy đủ với panel điều khiển
- ✅ Hiển thị bounding boxes màu sắc cho từng loại ung thư
- ✅ Điều chỉnh confidence threshold
- ✅ Hiển thị/ẩn confidence scores và tên class
- ✅ Kết quả chi tiết với đánh giá rủi ro
- ✅ Legend màu sắc
- ✅ Tóm tắt và khuyến nghị

### 🎯 Giao diện đơn giản (simple_gui.py)
- ✅ Giao diện tối giản, dễ sử dụng
- ✅ Chức năng cơ bản: chọn ảnh, phân tích, hiển thị kết quả
- ✅ Bounding boxes với màu sắc
- ✅ Kết quả detection ngắn gọn

## 🎨 Màu sắc Class

| Loại ung thư | Màu sắc | Code |
|-------------|---------|------|
| Adenocarcinoma | 🔴 Đỏ | (255, 0, 0) |
| Small Cell Carcinoma | 🟢 Xanh lá | (0, 255, 0) |
| Large Cell Carcinoma | 🔵 Xanh dương | (0, 0, 255) |
| Squamous Cell Carcinoma | 🟡 Vàng | (255, 255, 0) |

## 🚀 Cách sử dụng

### Chuẩn bị môi trường
```bash
# Kích hoạt môi trường ảo
source venv/bin/activate

# Kiểm tra dependencies
python -c "import tkinter, PIL, cv2, ultralytics; print('All OK')"
```

### Chạy GUI đầy đủ
```bash
# Sử dụng script
./run_gui.sh

# Hoặc chạy trực tiếp
python lung_cancer_gui.py
```

### Chạy GUI đơn giản
```bash
python simple_gui.py
```

## 📱 Hướng dẫn sử dụng

### 1. Chọn ảnh CT scan
- Click "📁 Select CT Scan Image"
- Chọn file ảnh (jpg, png, bmp, tiff)
- Ảnh sẽ được hiển thị trên canvas

### 2. Điều chỉnh settings (GUI đầy đủ)
- **Confidence Threshold**: Điều chỉnh độ nhạy (0.1 - 1.0)
- **Show confidence scores**: Hiển thị % tin cậy
- **Show class names**: Hiển thị tên loại ung thư

### 3. Phân tích ảnh
- Click "🔍 Analyze Image"
- Chờ model xử lý (vài giây)
- Xem kết quả với bounding boxes

### 4. Đọc kết quả

#### Bounding Boxes
- Màu khác nhau cho từng loại ung thư
- Label hiển thị tên + confidence %
- Kích thước box tương ứng vùng phát hiện

#### Mức độ rủi ro
- 🔴 **HIGH** (≥80%): Cần khám ngay
- 🟡 **MEDIUM** (≥60%): Nên kiểm tra thêm  
- 🟢 **LOW** (≥40%): Theo dõi
- ⚪ **VERY LOW** (<40%): Không chắc chắn

#### Khuyến nghị
- **Có detection**: Khám bệnh hoặc theo dõi
- **Không detection**: Tiếp tục theo dõi định kỳ

## 🛠️ Troubleshooting

### Lỗi thường gặp

#### "Model not found"
```bash
# Kiểm tra file model
ls -la best.pt

# Nếu không có, copy từ nơi khác
cp /path/to/your/best.pt .
```

#### "Import ultralytics failed"
```bash
# Cài đặt lại ultralytics
pip install ultralytics

# Hoặc upgrade
pip install --upgrade ultralytics
```

#### "Tkinter not available"
```bash
# macOS
brew install python-tk

# Ubuntu/Debian
sudo apt-get install python3-tk

# Windows: Tkinter có sẵn
```

#### Ảnh không hiển thị đúng
- Kiểm tra định dạng ảnh được hỗ trợ
- Thử resize ảnh nếu quá lớn
- Đảm bảo ảnh không bị lỗi

#### GUI chạy chậm
- Giảm độ phân giải ảnh
- Tăng confidence threshold
- Đóng các ứng dụng khác

## 📊 Model Performance

Model được train với kết quả:
- **Overall mAP50**: 98.2%
- **Overall mAP50-95**: 64.6%
- **Training time**: 3.496 hours (30 epochs)

Độ chính xác từng class:
- Adenocarcinoma: 99.0% mAP50
- Small Cell Carcinoma: 98.8% mAP50  
- Large Cell Carcinoma: 95.7% mAP50
- Squamous Cell Carcinoma: 99.2% mAP50

## ⚠️ Lưu ý quan trọng

1. **Mục đích nghiên cứu**: Công cụ này chỉ để nghiên cứu, không thay thế chẩn đoán y tế
2. **Tham khảo bác sĩ**: Luôn tham khảo ý kiến bác sĩ chuyên khoa
3. **Độ chính xác**: Model có thể có false positive/negative
4. **Dữ liệu**: Chỉ sử dụng ảnh CT scan chất lượng tốt

## 🔧 Tùy chỉnh

### Thay đổi màu sắc
Chỉnh sửa trong file .py:
```python
self.colors = {
    "Adenocarcinoma": (255, 0, 0),     # Đỏ
    "Small Cell Carcinoma": (0, 255, 0), # Xanh lá
    # ... thêm màu khác
}
```

### Thay đổi confidence mặc định
```python
self.conf_threshold = 0.25  # Thay đổi giá trị này
```

### Thêm loại ung thư mới
1. Train lại model với class mới
2. Cập nhật `self.colors` dictionary
3. Cập nhật legend trong GUI

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra các dependencies
2. Xem log lỗi trong terminal
3. Đảm bảo model file tồn tại
4. Thử với ảnh test khác
