#!/usr/bin/env python3
"""
Test script for SVM model (98% accuracy)
Script test cho model SVM (độ chính xác 98%)

Based on: huongdan.md
Author: AI Assistant
Date: September 2, 2025
"""

import pandas as pd
import joblib
import os

def test_model_loading():
    """Test loading SVM model and encoders"""
    print("🧪 TESTING SVM MODEL (98% ACCURACY)")
    print("=" * 50)
    
    # Check if model files exist
    model_files = {
        'SVM Model': 'model/svm_lung_cancer_98_model.pkl',
        'Gender Encoder': 'model/le_gender.pkl',
        'Cancer Encoder': 'model/le_cancer.pkl'
    }
    
    print("📁 Checking model files...")
    for name, path in model_files.items():
        if os.path.exists(path):
            size = os.path.getsize(path) / 1024  # KB
            print(f"✅ {name}: {path} ({size:.1f} KB)")
        else:
            print(f"❌ {name}: {path} - NOT FOUND")
            return False
    
    # Try to load models
    print("\n📊 Loading models...")
    try:
        rcv = joblib.load('model/svm_lung_cancer_98_model.pkl')
        le_gender = joblib.load('model/le_gender.pkl')
        le_cancer = joblib.load('model/le_cancer.pkl')
        
        print("✅ All models loaded successfully!")
        print(f"   📈 SVM Model type: {type(rcv).__name__}")
        print(f"   👤 Gender classes: {le_gender.classes_}")
        print(f"   🎯 Cancer classes: {le_cancer.classes_}")
        
        return rcv, le_gender, le_cancer
        
    except Exception as e:
        print(f"❌ Error loading models: {e}")
        return False

def test_prediction(rcv, le_gender, le_cancer):
    """Test prediction with sample data"""
    print("\n🧪 TESTING PREDICTION...")
    print("-" * 30)
    
    # Sample data from huongdan.md
    new_data = {
        'GENDER': ['M'],
        'AGE': [65],
        'SMOKING': [2],
        'YELLOW_FINGERS': [1],
        'ANXIETY': [2],
        'PEER_PRESSURE': [1],
        'CHRONIC DISEASE': [2],
        'FATIGUE ': [2],
        'ALLERGY ': [1],
        'WHEEZING': [2],
        'ALCOHOL CONSUMING': [1],
        'COUGHING': [2],
        'SHORTNESS OF BREATH': [1],
        'SWALLOWING DIFFICULTY': [2],
        'CHEST PAIN': [2]
    }
    
    print("📋 Input data:")
    for key, value in new_data.items():
        print(f"   {key}: {value[0]}")
    
    try:
        # Create DataFrame
        new_df = pd.DataFrame(new_data)
        
        # Preprocess according to huongdan.md
        binary_columns = ['SMOKING', 'YELLOW_FINGERS', 'ANXIETY', 'PEER_PRESSURE', 'CHRONIC DISEASE', 
                          'FATIGUE ', 'ALLERGY ', 'WHEEZING', 'ALCOHOL CONSUMING', 'COUGHING', 
                          'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN']
        
        print("\n🔄 Preprocessing data...")
        for col in binary_columns:
            original_value = new_df[col].iloc[0]
            new_df[col] = new_df[col].map({1: 0, 2: 1})
            new_value = new_df[col].iloc[0]
            print(f"   {col}: {original_value} → {new_value}")
        
        # Encode gender
        original_gender = new_df['GENDER'].iloc[0]
        new_df['GENDER'] = le_gender.transform(new_df['GENDER'])
        new_gender = new_df['GENDER'].iloc[0]
        print(f"   GENDER: {original_gender} → {new_gender}")
        
        # Make prediction
        print("\n🎯 Making prediction...")
        X_new = new_df
        y_pred_new = rcv.predict(X_new)
        y_pred_labels = le_cancer.inverse_transform(y_pred_new)
        
        print(f"✅ Prediction result: {y_pred_labels[0]}")
        print(f"   📊 Numeric prediction: {y_pred_new[0]}")
        
        # Try to get probabilities
        if hasattr(rcv, 'predict_proba'):
            proba = rcv.predict_proba(X_new)[0]
            print(f"   📈 Probabilities:")
            print(f"      • {le_cancer.classes_[0]}: {proba[0]:.3f} ({proba[0]*100:.1f}%)")
            print(f"      • {le_cancer.classes_[1]}: {proba[1]:.3f} ({proba[1]*100:.1f}%)")
        elif hasattr(rcv, 'decision_function'):
            decision = rcv.decision_function(X_new)[0]
            print(f"   📊 Decision function: {decision:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during prediction: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    # Test model loading
    models = test_model_loading()
    if not models:
        print("\n❌ Model loading failed. Exiting.")
        return
    
    rcv, le_gender, le_cancer = models
    
    # Test prediction
    success = test_prediction(rcv, le_gender, le_cancer)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ SVM model (98% accuracy) is working correctly")
        print("\n💡 Model is ready to use in your application!")
    else:
        print("❌ TESTS FAILED!")
        print("⚠️ Please check the model files and data format")
    
    print("\n📋 Model Summary:")
    print("   🎯 Accuracy: 98%")
    print("   📊 Type: SVM")
    print("   📈 Features: 15")
    print("   🔄 Preprocessing: Binary mapping + Gender encoding")

if __name__ == "__main__":
    main()
