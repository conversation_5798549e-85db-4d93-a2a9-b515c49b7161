#!/bin/bash

# Script để kích hoạt môi trường ảo và chạy detector

echo "🚀 Kích hoạt môi trường ảo và chạy Lung Cancer Detector..."

# Chuyển đến thư mục dự án
cd /Users/<USER>/Documents/python-file-hoc/projects/test-yolo

# Kích hoạt môi trường ảo
source venv/bin/activate

# Kiểm tra các thư viện đã cài đặt
echo "📦 Kiểm tra thư viện..."
python -c "import ultralytics; print('✅ Ultralytics OK')"
python -c "import cv2; print('✅ OpenCV OK')"

# Chạy detector
echo "🏥 Khởi động Lung Cancer Detector..."
python lung_cancer_detector.py

# Deactivate virtual environment when done
deactivate
