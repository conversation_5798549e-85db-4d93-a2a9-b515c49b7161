# 🏥 Lung Cancer Detection System

Simple and clean lung cancer prediction system using AI with 2 methods: health survey and CT scan image analysis.

## ✨ Key Features

### 📊 Survey Prediction
- **SVC Model**: Trained with real data from 309 patients
- **15 Features**: Gender, age, smoking and 12 symptoms
- **Accuracy**: 87.1% with AUC Score 95.8%
- **Risk Assessment**: LOW/MEDIUM/HIGH

### 🔍 CT Scan Analysis
- **YOLO Model**: Detects 4 types of lung cancer
- **Cancer Types**:
  - Adenocarcinoma
  - Small Cell Carcinoma  
  - Large Cell Carcinoma
  - Squamous Cell Carcinoma
- **Confidence Score**: Reliability for each detection

## 🚀 Installation & Usage


source venv/bin/activate && python app.py
source venv/bin/activate && python gui.py

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run Backend API
```bash
python app.py
```
- Web interface: http://localhost:8000
- API docs: http://localhost:8000/docs

### 3. Run Desktop GUI
```bash
python gui.py
```

## 📁 Project Structure (Cleaned)

```
lung-cancer-detection/
├── README.md              # Documentation
├── requirements.txt       # Minimal dependencies
├── app.py                # Unified backend API
├── gui.py                # Simple desktop GUI
├── data/                 # Training data
│   └── survey lung cancer.csv
└── model/                # AI Models
    ├── best.pt          # YOLO model
    └── svc_lung_cancer_new.pkl  # Trained SVC model
```

## 🔧 API Endpoints

### Survey Prediction
```http
POST /predict/survey
```
**Input**: 15 health parameters
**Output**: Prediction result + confidence score

### Image Analysis
```http
POST /predict/image  
```
**Input**: CT scan image file
**Output**: Detected cancer regions

## 📊 Model Performance

### SVC Model (Survey)
- **Accuracy**: 87.1%
- **AUC Score**: 95.8%
- **Training data**: 309 real samples

### YOLO Model (Image)
- **Classes**: 4 cancer types
- **Input**: CT scan images
- **Confidence threshold**: 0.25

## ⚠️ Medical Disclaimer

This system is for research and educational purposes only. Not a substitute for professional medical diagnosis. Always consult qualified healthcare professionals.

## 🎯 How to Use

### Backend API:
1. Run `python app.py`
2. Open http://localhost:8000
3. Use forms or API endpoints

### Desktop GUI:
1. Run `python gui.py`
2. "Survey" tab: Enter patient information
3. "Image" tab: Upload CT scan image
4. View prediction results

---

**Version**: 2.0.0 (Cleaned & Simplified)  
**Date**: September 2, 2025
