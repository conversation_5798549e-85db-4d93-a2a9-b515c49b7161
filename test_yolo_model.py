import cv2
import torch
from ultralytics import YOLO
import numpy as np
import os
from pathlib import Path
import matplotlib.pyplot as plt

class YOLOTester:
    def __init__(self, model_path):
        """
        Khởi tạo YOLO tester
        
        Args:
            model_path (str): Đ<PERSON>ờng dẫn đến file model .pt
        """
        self.model_path = model_path
        self.model = None
        self.load_model()
    
    def load_model(self):
        """Tải model YOLO"""
        try:
            self.model = YOLO(self.model_path)
            print(f"✅ Đã tải model thành công: {self.model_path}")
            print(f"📋 Các class được train: {self.model.names}")
        except Exception as e:
            print(f"❌ Lỗi khi tải model: {e}")
            return False
        return True
    
    def test_single_image(self, image_path, conf_threshold=0.25, save_result=True):
        """
        Test model với một hình ảnh
        
        Args:
            image_path (str): Đường dẫn đến hình ảnh
            conf_threshold (float): Ngưỡng confidence
            save_result (bool): <PERSON><PERSON> lưu kết quả không
        """
        if not os.path.exists(image_path):
            print(f"❌ Không tìm thấy hình ảnh: {image_path}")
            return None
        
        try:
            # Chạy inference
            results = self.model(image_path, conf=conf_threshold)
            
            # Lấy kết quả đầu tiên
            result = results[0]
            
            # In thông tin detection
            print(f"\n🔍 Kết quả detection cho: {os.path.basename(image_path)}")
            print(f"📊 Số objects phát hiện: {len(result.boxes)}")
            
            if len(result.boxes) > 0:
                for i, box in enumerate(result.boxes):
                    class_id = int(box.cls[0])
                    confidence = float(box.conf[0])
                    class_name = self.model.names[class_id]
                    coords = box.xyxy[0].tolist()
                    
                    print(f"  {i+1}. {class_name}: {confidence:.2f} - Tọa độ: {coords}")
            else:
                print("  Không phát hiện object nào")
            
            # Vẽ kết quả lên ảnh
            annotated_image = result.plot()
            
            # Hiển thị ảnh
            self.display_results(annotated_image, os.path.basename(image_path))
            
            # Lưu kết quả nếu được yêu cầu
            if save_result:
                output_path = f"results_{os.path.basename(image_path)}"
                cv2.imwrite(output_path, annotated_image)
                print(f"💾 Đã lưu kết quả: {output_path}")
            
            return results
            
        except Exception as e:
            print(f"❌ Lỗi khi xử lý ảnh: {e}")
            return None
    
    def test_multiple_images(self, image_folder, conf_threshold=0.25):
        """
        Test model với nhiều hình ảnh trong thư mục
        
        Args:
            image_folder (str): Đường dẫn đến thư mục chứa ảnh
            conf_threshold (float): Ngưỡng confidence
        """
        if not os.path.exists(image_folder):
            print(f"❌ Không tìm thấy thư mục: {image_folder}")
            return
        
        # Các định dạng ảnh được hỗ trợ
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # Tìm tất cả file ảnh
        image_files = []
        for file in os.listdir(image_folder):
            if Path(file).suffix.lower() in image_extensions:
                image_files.append(os.path.join(image_folder, file))
        
        if not image_files:
            print(f"❌ Không tìm thấy ảnh nào trong thư mục: {image_folder}")
            return
        
        print(f"📁 Tìm thấy {len(image_files)} ảnh để test")
        
        # Test từng ảnh
        for image_path in image_files:
            self.test_single_image(image_path, conf_threshold, save_result=True)
            print("-" * 50)
    
    def test_webcam(self, conf_threshold=0.25):
        """
        Test model với webcam real-time
        
        Args:
            conf_threshold (float): Ngưỡng confidence
        """
        print("📹 Khởi động webcam (nhấn 'q' để thoát)...")
        
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Không thể mở webcam")
            return
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Chạy inference
                results = self.model(frame, conf=conf_threshold)
                
                # Vẽ kết quả
                annotated_frame = results[0].plot()
                
                # Hiển thị
                cv2.imshow('YOLO Real-time Detection', annotated_frame)
                
                # Thoát khi nhấn 'q'
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                    
        except Exception as e:
            print(f"❌ Lỗi webcam: {e}")
        finally:
            cap.release()
            cv2.destroyAllWindows()
    
    def display_results(self, image, title="Detection Results"):
        """Hiển thị kết quả với matplotlib"""
        plt.figure(figsize=(12, 8))
        # Chuyển từ BGR sang RGB cho matplotlib
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        plt.imshow(image_rgb)
        plt.title(title)
        plt.axis('off')
        plt.tight_layout()
        plt.show()
    
    def get_model_info(self):
        """Hiển thị thông tin về model"""
        if self.model is None:
            print("❌ Model chưa được tải")
            return
        
        print("📋 THÔNG TIN MODEL")
        print("=" * 30)
        print(f"Model path: {self.model_path}")
        print(f"Classes: {list(self.model.names.values())}")
        print(f"Number of classes: {len(self.model.names)}")
        
        # Thông tin thêm nếu có
        try:
            model_info = self.model.info()
            print(f"Model info: {model_info}")
        except:
            pass


def main():
    """Hàm main để test model"""
    
    # Đường dẫn đến model
    model_path = "/Users/<USER>/Documents/python-file-hoc/projects/test-yolo/best.pt"
    
    # Khởi tạo tester
    tester = YOLOTester(model_path)
    
    # Hiển thị thông tin model
    tester.get_model_info()
    
    print("\n" + "="*50)
    print("🎯 CHỌN CHỨC NĂNG TEST:")
    print("1. Test với một ảnh cụ thể")
    print("2. Test với tất cả ảnh trong thư mục test_images")
    print("3. Test với webcam real-time")
    print("4. Hiển thị thông tin model")
    print("="*50)
    
    choice = input("Nhập lựa chọn (1-4): ").strip()
    
    if choice == "1":
        image_path = input("Nhập đường dẫn đến ảnh: ").strip()
        conf = float(input("Nhập confidence threshold (0.1-1.0, mặc định 0.25): ") or "0.25")
        tester.test_single_image(image_path, conf_threshold=conf)
        
    elif choice == "2":
        folder_path = input("Nhập đường dẫn thư mục (mặc định: ./test_images): ").strip()
        if not folder_path:
            folder_path = "./test_images"
        conf = float(input("Nhập confidence threshold (0.1-1.0, mặc định 0.25): ") or "0.25")
        tester.test_multiple_images(folder_path, conf_threshold=conf)
        
    elif choice == "3":
        conf = float(input("Nhập confidence threshold (0.1-1.0, mặc định 0.25): ") or "0.25")
        tester.test_webcam(conf_threshold=conf)
        
    elif choice == "4":
        tester.get_model_info()
        
    else:
        print("❌ Lựa chọn không hợp lệ!")


if __name__ == "__main__":
    main()
