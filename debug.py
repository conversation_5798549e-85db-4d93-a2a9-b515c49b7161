#!/usr/bin/env python3
"""
Debug script to check numpy and other dependencies
"""

def check_imports():
    """Kiểm tra từng import một cách chi tiết"""
    print("🔍 CHECKING IMPORTS...")
    print("=" * 40)
    
    # Check numpy
    try:
        import numpy as np
        print(f"✅ numpy: {np.__version__}")
        
        # Test basic numpy functionality
        arr = np.array([1, 2, 3])
        print(f"✅ numpy basic test: {arr}")
        
    except ImportError as e:
        print(f"❌ numpy import error: {e}")
        return False
    except Exception as e:
        print(f"❌ numpy error: {e}")
        return False
    
    # Check OpenCV
    try:
        import cv2
        print(f"✅ cv2: {cv2.__version__}")
        
        # Test basic OpenCV
        img = cv2.imread("best.pt")  # This will fail but won't crash
        print("✅ cv2 basic test: OK")
        
    except ImportError as e:
        print(f"❌ cv2 import error: {e}")
        return False
    except Exception as e:
        print(f"✅ cv2: loaded (expected error for non-image file)")
    
    # Check PIL
    try:
        from PIL import Image, ImageTk
        print(f"✅ PIL: {Image.__version__}")
    except ImportError as e:
        print(f"❌ PIL import error: {e}")
        return False
    
    # Check ultralytics
    try:
        from ultralytics import YOLO
        print("✅ ultralytics: imported")
        
        # Try to load model
        try:
            model = YOLO("best.pt")
            print("✅ YOLO model: loaded successfully")
            print(f"✅ Model classes: {list(model.names.values())}")
            return True
        except Exception as e:
            print(f"❌ YOLO model error: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ ultralytics import error: {e}")
        return False

def test_simple_detection():
    """Test detection đơn giản"""
    try:
        print("\n🧪 TESTING SIMPLE DETECTION...")
        print("=" * 40)
        
        import numpy as np
        import cv2
        from ultralytics import YOLO
        
        # Tạo ảnh test đơn giản
        test_image = np.zeros((640, 640, 3), dtype=np.uint8)
        test_image[:] = (128, 128, 128)  # Gray background
        
        # Load model
        model = YOLO("best.pt")
        
        # Run detection
        results = model(test_image, conf=0.1)
        result = results[0]
        
        print(f"✅ Detection completed: {len(result.boxes)} detections")
        return True
        
    except Exception as e:
        print(f"❌ Detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🩺 LUNG CANCER DETECTION - DEBUG")
    print("=" * 50)
    
    # Check imports
    if not check_imports():
        print("\n❌ Import check failed!")
        return
    
    # Test detection
    test_simple_detection()
    
    print("\n📋 DEBUG SUMMARY:")
    print("If all tests passed, the GUI should work.")
    print("Try running: python simple_gui.py")

if __name__ == "__main__":
    main()
