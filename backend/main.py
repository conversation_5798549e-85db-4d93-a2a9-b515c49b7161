#!/usr/bin/env python3
"""
Backend API cho Lung Cancer Detection System
Tích hợp Survey Model, YOLO Model và Gemini API
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
import cv2
import numpy as np
import pickle
import pandas as pd
from PIL import Image
import io
import base64
from typing import List, Optional
import logging
from datetime import datetime

# Import models
from ultralytics import YOLO
import google.generativeai as genai

# Import local modules
from config import config
from gemini_service import gemini_service

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Lung Cancer Detection API",
    description="API tích hợp Survey Model, YOLO Detection và Gemini AI Analysis",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for models
survey_model = None
yolo_model = None
gemini_model = None

# Survey features mapping
SURVEY_FEATURES = [
    'GENDER', 'AGE', 'SMOKING', 'YELLOW_FINGERS', 'ANXIETY',
    'PEER_PRESSURE', 'CHRONIC DISEASE', 'FATIGUE', 'ALLERGY',
    'WHEEZING', 'ALCOHOL CONSUMING', 'COUGHING',
    'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN'
]

# Cancer types mapping
CANCER_TYPES = {
    0: "Adenocarcinoma",
    1: "Large Cell Carcinoma",
    2: "Small Cell Carcinoma",
    3: "Squamous Cell Carcinoma"
}

@app.on_event("startup")
async def startup_event():
    """Load models on startup"""
    global survey_model, yolo_model, gemini_model

    try:
        # Load survey model
        survey_model_path = "../model/new_lung_cancer_model.pkl"
        if os.path.exists(survey_model_path):
            with open(survey_model_path, 'rb') as f:
                survey_model = pickle.load(f)
            logger.info("✅ Survey model loaded successfully")
        else:
            logger.error(f"❌ Survey model not found: {survey_model_path}")

        # Load YOLO model
        yolo_model_path = "../model/best.pt"
        if os.path.exists(yolo_model_path):
            yolo_model = YOLO(yolo_model_path)
            logger.info("✅ YOLO model loaded successfully")
        else:
            logger.error(f"❌ YOLO model not found: {yolo_model_path}")

        # Configure Gemini
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        if gemini_api_key:
            genai.configure(api_key=gemini_api_key)
            gemini_model = genai.GenerativeModel('gemini-pro')
            logger.info("✅ Gemini API configured successfully")
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found in environment variables")

    except Exception as e:
        logger.error(f"❌ Error loading models: {str(e)}")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Lung Cancer Detection API",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "models": {
            "survey_model": survey_model is not None,
            "yolo_model": yolo_model is not None,
            "gemini_model": gemini_model is not None
        }
    }

@app.post("/predict/survey")
async def predict_survey(
    gender: int = Form(..., description="0: Female, 1: Male"),
    age: int = Form(..., description="Age in years"),
    smoking: int = Form(..., description="0: No, 1: Yes"),
    yellow_fingers: int = Form(..., description="0: No, 1: Yes"),
    anxiety: int = Form(..., description="0: No, 1: Yes"),
    peer_pressure: int = Form(..., description="0: No, 1: Yes"),
    chronic_disease: int = Form(..., description="0: No, 1: Yes"),
    fatigue: int = Form(..., description="0: No, 1: Yes"),
    allergy: int = Form(..., description="0: No, 1: Yes"),
    wheezing: int = Form(..., description="0: No, 1: Yes"),
    alcohol_consuming: int = Form(..., description="0: No, 1: Yes"),
    coughing: int = Form(..., description="0: No, 1: Yes"),
    shortness_of_breath: int = Form(..., description="0: No, 1: Yes"),
    swallowing_difficulty: int = Form(..., description="0: No, 1: Yes"),
    chest_pain: int = Form(..., description="0: No, 1: Yes")
):
    """Predict lung cancer from survey data"""
    if survey_model is None:
        raise HTTPException(status_code=503, detail="Survey model not loaded")

    try:
        # Prepare input data
        input_data = np.array([[
            gender, age, smoking, yellow_fingers, anxiety, peer_pressure,
            chronic_disease, fatigue, allergy, wheezing, alcohol_consuming,
            coughing, shortness_of_breath, swallowing_difficulty, chest_pain
        ]])

        # Make prediction
        prediction = survey_model.predict(input_data)[0]
        probability = survey_model.predict_proba(input_data)[0]

        # Get confidence scores
        no_cancer_prob = float(probability[0])
        cancer_prob = float(probability[1])

        result = {
            "prediction": "YES" if prediction == 1 else "NO",
            "confidence": {
                "no_cancer": no_cancer_prob,
                "cancer": cancer_prob
            },
            "risk_level": "HIGH" if cancer_prob > 0.7 else "MEDIUM" if cancer_prob > 0.3 else "LOW",
            "input_features": dict(zip(SURVEY_FEATURES, [float(x) for x in input_data[0]]))
        }

        return result

    except Exception as e:
        logger.error(f"Survey prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/predict/image")
async def predict_image(file: UploadFile = File(...)):
    """Detect cancer from CT scan image"""
    if yolo_model is None:
        raise HTTPException(status_code=503, detail="YOLO model not loaded")

    try:
        # Read and process image
        contents = await file.read()
        nparr = np.frombuffer(contents, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if image is None:
            raise HTTPException(status_code=400, detail="Invalid image format")

        # Run YOLO detection
        results = yolo_model(image, conf=0.25)
        result = results[0]

        detections = []
        annotated_image = image.copy()

        if len(result.boxes) > 0:
            # Process each detection
            for box in result.boxes:
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                coords = box.xyxy[0].cpu().numpy().astype(int)

                cancer_type = CANCER_TYPES.get(class_id, "Unknown")

                detection = {
                    "cancer_type": cancer_type,
                    "confidence": confidence,
                    "bbox": {
                        "x1": int(coords[0]),
                        "y1": int(coords[1]),
                        "x2": int(coords[2]),
                        "y2": int(coords[3])
                    }
                }
                detections.append(detection)

                # Draw bounding box
                color = get_color_for_cancer_type(cancer_type)
                cv2.rectangle(annotated_image, (coords[0], coords[1]), (coords[2], coords[3]), color, 3)

                # Draw label
                label = f"{cancer_type} {confidence:.1%}"
                (text_width, text_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)
                cv2.rectangle(annotated_image,
                             (coords[0], coords[1] - text_height - 10),
                             (coords[0] + text_width, coords[1]),
                             color, -1)
                cv2.putText(annotated_image, label, (coords[0], coords[1] - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

        # Convert annotated image to base64
        _, buffer = cv2.imencode('.jpg', annotated_image)
        img_base64 = base64.b64encode(buffer).decode('utf-8')

        result_data = {
            "detections": detections,
            "total_detections": len(detections),
            "annotated_image": img_base64,
            "status": "cancer_detected" if detections else "no_cancer_detected"
        }

        return result_data

    except Exception as e:
        logger.error(f"Image prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")

@app.post("/analyze/combined")
async def analyze_combined(
    # Survey data
    gender: int = Form(...),
    age: int = Form(...),
    smoking: int = Form(...),
    yellow_fingers: int = Form(...),
    anxiety: int = Form(...),
    peer_pressure: int = Form(...),
    chronic_disease: int = Form(...),
    fatigue: int = Form(...),
    allergy: int = Form(...),
    wheezing: int = Form(...),
    alcohol_consuming: int = Form(...),
    coughing: int = Form(...),
    shortness_of_breath: int = Form(...),
    swallowing_difficulty: int = Form(...),
    chest_pain: int = Form(...),
    # Image file
    file: Optional[UploadFile] = File(None)
):
    """Combined analysis using both survey and image data with Gemini AI insights"""

    # Get survey prediction
    survey_result = None
    if survey_model is not None:
        try:
            input_data = np.array([[
                gender, age, smoking, yellow_fingers, anxiety, peer_pressure,
                chronic_disease, fatigue, allergy, wheezing, alcohol_consuming,
                coughing, shortness_of_breath, swallowing_difficulty, chest_pain
            ]])

            prediction = survey_model.predict(input_data)[0]
            probability = survey_model.predict_proba(input_data)[0]

            survey_result = {
                "prediction": "YES" if prediction == 1 else "NO",
                "confidence": {
                    "no_cancer": float(probability[0]),
                    "cancer": float(probability[1])
                },
                "risk_level": "HIGH" if probability[1] > 0.7 else "MEDIUM" if probability[1] > 0.3 else "LOW"
            }
        except Exception as e:
            logger.error(f"Survey analysis error: {str(e)}")

    # Get image prediction
    image_result = None
    if file and yolo_model is not None:
        try:
            contents = await file.read()
            nparr = np.frombuffer(contents, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is not None:
                results = yolo_model(image, conf=0.25)
                result = results[0]

                detections = []
                if len(result.boxes) > 0:
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        cancer_type = CANCER_TYPES.get(class_id, "Unknown")

                        detections.append({
                            "cancer_type": cancer_type,
                            "confidence": confidence
                        })

                image_result = {
                    "detections": detections,
                    "total_detections": len(detections),
                    "status": "cancer_detected" if detections else "no_cancer_detected"
                }
        except Exception as e:
            logger.error(f"Image analysis error: {str(e)}")

    # Get Gemini AI analysis
    gemini_analysis = await gemini_service.analyze_combined_results(
        survey_result,
        image_result,
        {
            "age": age,
            "gender": "Male" if gender == 1 else "Female",
            "smoking": "Yes" if smoking == 1 else "No"
        }
    )

    return {
        "survey_analysis": survey_result,
        "image_analysis": image_result,
        "ai_analysis": gemini_analysis,
        "timestamp": datetime.now().isoformat()
    }



def get_color_for_cancer_type(cancer_type: str) -> tuple:
    """Get BGR color for cancer type"""
    colors = {
        "Adenocarcinoma": (0, 0, 255),  # Red
        "Small Cell Carcinoma": (0, 255, 0),  # Green
        "Large Cell Carcinoma": (255, 0, 0),  # Blue
        "Squamous Cell Carcinoma": (0, 255, 255)  # Yellow
    }
    return colors.get(cancer_type, (255, 255, 255))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)