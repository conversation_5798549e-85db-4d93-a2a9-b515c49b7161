#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a new lung cancer survey model
"""

import numpy as np
import pandas as pd
import pickle
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Survey features as defined in the system
SURVEY_FEATURES = [
    'GENDER', 'AGE', 'SMOKING', 'YELLOW_FINGERS', 'ANXIETY', 'PEER_PRESSURE',
    'CHRONIC DISEASE', 'FATIGUE', 'ALLERGY', 'WHEEZING', 'ALCOHOL CONSUMING',
    'COUGHING', 'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN'
]

def generate_synthetic_data(n_samples=10000):
    """Generate synthetic lung cancer survey data"""
    np.random.seed(42)
    
    data = []
    
    for _ in range(n_samples):
        # Generate features with realistic correlations
        age = np.random.randint(20, 90)
        gender = np.random.randint(0, 2)  # 0: Female, 1: Male
        
        # Smoking increases risk
        smoking = np.random.choice([0, 1], p=[0.7, 0.3])
        
        # Age and smoking influence other symptoms
        base_risk = (age - 20) / 70 + smoking * 0.3
        
        # Generate correlated symptoms
        yellow_fingers = np.random.choice([0, 1], p=[1-min(0.8, smoking*0.6 + 0.1), min(0.8, smoking*0.6 + 0.1)])
        anxiety = np.random.choice([0, 1], p=[0.8, 0.2])
        peer_pressure = np.random.choice([0, 1], p=[0.9, 0.1])
        chronic_disease = np.random.choice([0, 1], p=[1-min(0.4, base_risk*0.3 + 0.1), min(0.4, base_risk*0.3 + 0.1)])
        fatigue = np.random.choice([0, 1], p=[1-min(0.6, base_risk*0.4 + 0.2), min(0.6, base_risk*0.4 + 0.2)])
        allergy = np.random.choice([0, 1], p=[0.8, 0.2])
        wheezing = np.random.choice([0, 1], p=[1-min(0.5, base_risk*0.3 + 0.1), min(0.5, base_risk*0.3 + 0.1)])
        alcohol = np.random.choice([0, 1], p=[0.6, 0.4])
        coughing = np.random.choice([0, 1], p=[1-min(0.7, base_risk*0.5 + 0.2), min(0.7, base_risk*0.5 + 0.2)])
        shortness_breath = np.random.choice([0, 1], p=[1-min(0.6, base_risk*0.4 + 0.1), min(0.6, base_risk*0.4 + 0.1)])
        swallowing_diff = np.random.choice([0, 1], p=[1-min(0.4, base_risk*0.2 + 0.05), min(0.4, base_risk*0.2 + 0.05)])
        chest_pain = np.random.choice([0, 1], p=[1-min(0.5, base_risk*0.3 + 0.1), min(0.5, base_risk*0.3 + 0.1)])
        
        # Calculate cancer probability based on risk factors
        cancer_prob = base_risk * 0.3
        cancer_prob += (yellow_fingers + chronic_disease + fatigue + wheezing + 
                       coughing + shortness_breath + swallowing_diff + chest_pain) * 0.08
        cancer_prob = min(0.9, max(0.05, cancer_prob))
        
        # Generate target
        lung_cancer = np.random.choice([0, 1], p=[1-cancer_prob, cancer_prob])
        
        data.append([
            gender, age, smoking, yellow_fingers, anxiety, peer_pressure,
            chronic_disease, fatigue, allergy, wheezing, alcohol,
            coughing, shortness_breath, swallowing_diff, chest_pain, lung_cancer
        ])
    
    columns = SURVEY_FEATURES + ['LUNG_CANCER']
    df = pd.DataFrame(data, columns=columns)
    return df

class EnsembleLungCancerModel:
    """Ensemble model for lung cancer prediction"""
    
    def __init__(self):
        self.models = {
            'rf': RandomForestClassifier(n_estimators=100, random_state=42),
            'gb': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'et': ExtraTreesClassifier(n_estimators=100, random_state=42),
            'mlp': MLPClassifier(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42)
        }
        self.scaler = StandardScaler()
        self.is_fitted = False
    
    def fit(self, X, y):
        """Train the ensemble model"""
        # Scale features for neural network
        X_scaled = self.scaler.fit_transform(X)
        
        # Train each model
        for name, model in self.models.items():
            if name == 'mlp':
                model.fit(X_scaled, y)
            else:
                model.fit(X, y)
        
        self.is_fitted = True
        return self
    
    def predict(self, X):
        """Make predictions using ensemble voting"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        X_scaled = self.scaler.transform(X)
        predictions = []
        
        for name, model in self.models.items():
            if name == 'mlp':
                pred = model.predict(X_scaled)
            else:
                pred = model.predict(X)
            predictions.append(pred)
        
        # Majority voting
        predictions = np.array(predictions)
        final_pred = np.apply_along_axis(lambda x: np.bincount(x).argmax(), axis=0, arr=predictions)
        return final_pred
    
    def predict_proba(self, X):
        """Get prediction probabilities using ensemble averaging"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        X_scaled = self.scaler.transform(X)
        probabilities = []
        
        for name, model in self.models.items():
            if name == 'mlp':
                prob = model.predict_proba(X_scaled)
            else:
                prob = model.predict_proba(X)
            probabilities.append(prob)
        
        # Average probabilities
        avg_prob = np.mean(probabilities, axis=0)
        return avg_prob

def main():
    """Create and save the lung cancer survey model"""
    print("🏥 Creating Lung Cancer Survey Model")
    print("=" * 50)

    # Generate synthetic data
    print("📊 Generating synthetic training data...")
    df = generate_synthetic_data(10000)

    # Prepare features and target
    X = df[SURVEY_FEATURES].values
    y = df['LUNG_CANCER'].values

    print(f"✅ Generated {len(df)} samples")
    print(f"📈 Cancer cases: {y.sum()} ({y.mean()*100:.1f}%)")

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    # Create and train simple RandomForest model (more compatible)
    print("🤖 Training RandomForest model...")
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)

    # Evaluate model
    print("📊 Evaluating model performance...")
    y_pred = model.predict(X_test)
    y_prob = model.predict_proba(X_test)

    accuracy = accuracy_score(y_test, y_pred)
    auc_score = roc_auc_score(y_test, y_prob[:, 1])

    print(f"✅ Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"✅ AUC Score: {auc_score:.4f} ({auc_score*100:.2f}%)")

    # Save model
    model_path = "model/new_lung_cancer_model.pkl"
    print(f"💾 Saving model to {model_path}...")

    with open(model_path, 'wb') as f:
        pickle.dump(model, f)

    print("✅ Model saved successfully!")

    # Test loading
    print("🔍 Testing model loading...")
    with open(model_path, 'rb') as f:
        loaded_model = pickle.load(f)

    # Test prediction
    test_sample = X_test[:1]
    pred = loaded_model.predict(test_sample)
    prob = loaded_model.predict_proba(test_sample)

    print(f"✅ Model loaded and tested successfully!")
    print(f"📋 Test prediction: {pred[0]} (prob: {prob[0][1]:.3f})")

    print("\n🎉 Model creation completed!")
    print("The new model is compatible with the current scikit-learn version.")

if __name__ == "__main__":
    main()
