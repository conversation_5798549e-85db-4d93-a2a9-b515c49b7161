#!/usr/bin/env python3
"""
Lung Cancer Predictor using trained SVC model
D<PERSON> đoán ung thư phổi sử dụng model SVC đã train

Author: AI Assistant
Date: September 2, 2025
"""

import joblib
import numpy as np
import pandas as pd
import os
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class LungCancerPredictor:
    """Class để dự đoán ung thư phổi sử dụng model SVC đã train"""
    
    def __init__(self, model_path: str = "model/svc_lung_cancer_new.pkl"):
        """
        Khởi tạo predictor

        Args:
            model_path (str): Đường dẫn đến model SVC đã train
        """
        self.model_path = model_path
        self.model = None
        self.scaler = None
        self.feature_names = [
            'GENDER', 'AGE', 'SMOKING', 'YELLOW_FINGERS', 'ANXIETY',
            'PEER_PRESSURE', 'CHRONIC DISEASE', 'FATIGUE ', 'ALLERGY ',
            'WHEEZING', 'ALCOHOL CONSUMING', 'COUGHING',
            'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN'
        ]
        self.load_model()
    
    def load_model(self) -> bool:
        """
        Tải model SVC đã train

        Returns:
            bool: True nếu tải thành công, False nếu thất bại
        """
        try:
            if not os.path.exists(self.model_path):
                print(f"❌ Model file không tồn tại: {self.model_path}")
                return False

            # Load model package
            model_package = joblib.load(self.model_path)

            # Kiểm tra xem có phải là package không
            if isinstance(model_package, dict) and 'model' in model_package:
                self.model = model_package['model']
                self.scaler = model_package.get('scaler', None)
                self.feature_names = model_package.get('feature_names', self.feature_names)
                print(f"✅ Đã tải model package thành công: {self.model_path}")
                print(f"📋 Model type: {type(self.model).__name__}")
                print(f"📋 Scaler: {'Có' if self.scaler else 'Không'}")
            else:
                # Backward compatibility - model cũ
                self.model = model_package
                self.scaler = None
                print(f"✅ Đã tải model cũ thành công: {self.model_path}")
                print(f"📋 Model type: {type(self.model).__name__}")

            # Kiểm tra xem model có phương thức predict_proba không
            if hasattr(self.model, 'predict_proba'):
                print("✅ Model hỗ trợ predict_proba")
            else:
                print("⚠️ Model không hỗ trợ predict_proba")

            return True

        except Exception as e:
            print(f"❌ Lỗi khi tải model: {e}")
            return False
    
    def predict_single(self, features: List[float]) -> Dict:
        """
        Dự đoán cho một mẫu dữ liệu
        
        Args:
            features (List[float]): Danh sách 15 đặc trưng theo thứ tự
        
        Returns:
            Dict: Kết quả dự đoán
        """
        if self.model is None:
            raise ValueError("Model chưa được tải. Vui lòng gọi load_model() trước.")
        
        if len(features) != 15:
            raise ValueError(f"Cần đúng 15 đặc trưng, nhận được {len(features)}")
        
        try:
            # Chuyển đổi thành numpy array
            X = np.array([features])

            # Chuẩn hóa dữ liệu nếu có scaler
            if self.scaler is not None:
                X = self.scaler.transform(X)

            # Dự đoán
            prediction = self.model.predict(X)[0]

            # Tính xác suất nếu có thể
            probabilities = None
            if hasattr(self.model, 'predict_proba'):
                proba = self.model.predict_proba(X)[0]
                probabilities = {
                    'no_cancer': float(proba[0]),
                    'cancer': float(proba[1])
                }
            elif hasattr(self.model, 'decision_function'):
                # Sử dụng decision function cho SVM
                decision_score = self.model.decision_function(X)[0]
                # Chuyển đổi thành xác suất gần đúng
                prob_cancer = 1 / (1 + np.exp(-decision_score))
                probabilities = {
                    'no_cancer': float(1 - prob_cancer),
                    'cancer': float(prob_cancer)
                }
            
            # Xác định mức độ rủi ro
            risk_level = "LOW"
            if probabilities:
                cancer_prob = probabilities['cancer']
                if cancer_prob > 0.7:
                    risk_level = "HIGH"
                elif cancer_prob > 0.3:
                    risk_level = "MEDIUM"
            
            result = {
                'prediction': 'YES' if prediction == 1 else 'NO',
                'prediction_numeric': int(prediction),
                'probabilities': probabilities,
                'risk_level': risk_level,
                'input_features': dict(zip(self.feature_names, features))
            }
            
            return result
            
        except Exception as e:
            raise Exception(f"Lỗi khi dự đoán: {e}")
    
    def predict_batch(self, features_list: List[List[float]]) -> List[Dict]:
        """
        Dự đoán cho nhiều mẫu dữ liệu
        
        Args:
            features_list (List[List[float]]): Danh sách các mẫu dữ liệu
        
        Returns:
            List[Dict]: Danh sách kết quả dự đoán
        """
        results = []
        for i, features in enumerate(features_list):
            try:
                result = self.predict_single(features)
                result['sample_id'] = i
                results.append(result)
            except Exception as e:
                results.append({
                    'sample_id': i,
                    'error': str(e),
                    'prediction': None
                })
        
        return results
    
    def predict_from_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Dự đoán từ DataFrame
        
        Args:
            df (pd.DataFrame): DataFrame chứa dữ liệu với các cột đặc trưng
        
        Returns:
            pd.DataFrame: DataFrame với kết quả dự đoán
        """
        if self.model is None:
            raise ValueError("Model chưa được tải.")
        
        # Kiểm tra các cột cần thiết
        missing_cols = [col for col in self.feature_names if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Thiếu các cột: {missing_cols}")
        
        # Lấy dữ liệu đặc trưng
        X = df[self.feature_names].values
        
        # Dự đoán
        predictions = self.model.predict(X)
        
        # Tính xác suất
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(X)
            df['cancer_probability'] = probabilities[:, 1]
            df['no_cancer_probability'] = probabilities[:, 0]
        elif hasattr(self.model, 'decision_function'):
            decision_scores = self.model.decision_function(X)
            cancer_probs = 1 / (1 + np.exp(-decision_scores))
            df['cancer_probability'] = cancer_probs
            df['no_cancer_probability'] = 1 - cancer_probs
        
        # Thêm kết quả dự đoán
        df['prediction'] = predictions
        df['prediction_text'] = ['YES' if p == 1 else 'NO' for p in predictions]
        
        # Thêm mức độ rủi ro
        if 'cancer_probability' in df.columns:
            df['risk_level'] = df['cancer_probability'].apply(
                lambda x: 'HIGH' if x > 0.7 else 'MEDIUM' if x > 0.3 else 'LOW'
            )
        
        return df
    
    def get_feature_info(self) -> Dict:
        """
        Lấy thông tin về các đặc trưng
        
        Returns:
            Dict: Thông tin về các đặc trưng
        """
        feature_info = {
            'GENDER': 'Giới tính (0: Nữ, 1: Nam)',
            'AGE': 'Tuổi (số nguyên)',
            'SMOKING': 'Hút thuốc (0: Không, 1: Có)',
            'YELLOW_FINGERS': 'Ngón tay vàng (0: Không, 1: Có)',
            'ANXIETY': 'Lo âu (0: Không, 1: Có)',
            'PEER_PRESSURE': 'Áp lực bạn bè (0: Không, 1: Có)',
            'CHRONIC DISEASE': 'Bệnh mãn tính (0: Không, 1: Có)',
            'FATIGUE': 'Mệt mỏi (0: Không, 1: Có)',
            'ALLERGY': 'Dị ứng (0: Không, 1: Có)',
            'WHEEZING': 'Khò khè (0: Không, 1: Có)',
            'ALCOHOL CONSUMING': 'Uống rượu (0: Không, 1: Có)',
            'COUGHING': 'Ho (0: Không, 1: Có)',
            'SHORTNESS OF BREATH': 'Khó thở (0: Không, 1: Có)',
            'SWALLOWING DIFFICULTY': 'Khó nuốt (0: Không, 1: Có)',
            'CHEST PAIN': 'Đau ngực (0: Không, 1: Có)'
        }
        
        return feature_info
    
    def create_sample_data(self) -> List[float]:
        """
        Tạo dữ liệu mẫu để test
        
        Returns:
            List[float]: Dữ liệu mẫu
        """
        # Dữ liệu mẫu: Nam, 65 tuổi, hút thuốc, có một số triệu chứng
        sample = [
            1,    # GENDER: Nam
            65,   # AGE: 65 tuổi
            1,    # SMOKING: Có hút thuốc
            1,    # YELLOW_FINGERS: Có
            0,    # ANXIETY: Không
            0,    # PEER_PRESSURE: Không
            1,    # CHRONIC DISEASE: Có
            1,    # FATIGUE: Có
            0,    # ALLERGY: Không
            1,    # WHEEZING: Có
            1,    # ALCOHOL CONSUMING: Có
            1,    # COUGHING: Có
            1,    # SHORTNESS OF BREATH: Có
            0,    # SWALLOWING DIFFICULTY: Không
            1     # CHEST PAIN: Có
        ]
        
        return sample


def main():
    """Hàm main để test predictor"""
    print("🏥 LUNG CANCER PREDICTOR - SVC MODEL")
    print("=" * 50)
    
    # Khởi tạo predictor
    predictor = LungCancerPredictor()
    
    if predictor.model is None:
        print("❌ Không thể tải model. Thoát chương trình.")
        return
    
    # Hiển thị thông tin đặc trưng
    print("\n📋 THÔNG TIN CÁC ĐẶC TRƯNG:")
    print("-" * 30)
    feature_info = predictor.get_feature_info()
    for i, (feature, description) in enumerate(feature_info.items(), 1):
        print(f"{i:2d}. {feature}: {description}")
    
    # Test với dữ liệu mẫu
    print("\n🧪 TEST VỚI DỮ LIỆU MẪU:")
    print("-" * 30)
    sample_data = predictor.create_sample_data()
    
    print("Dữ liệu đầu vào:")
    for i, (feature, value) in enumerate(zip(predictor.feature_names, sample_data)):
        print(f"  {feature}: {value}")
    
    try:
        result = predictor.predict_single(sample_data)
        
        print(f"\n📊 KẾT QUẢ DỰ ĐOÁN:")
        print(f"  Dự đoán: {result['prediction']}")
        print(f"  Mức độ rủi ro: {result['risk_level']}")
        
        if result['probabilities']:
            print(f"  Xác suất không ung thư: {result['probabilities']['no_cancer']:.3f}")
            print(f"  Xác suất ung thư: {result['probabilities']['cancer']:.3f}")
        
    except Exception as e:
        print(f"❌ Lỗi khi dự đoán: {e}")
    
    print("\n✅ Test hoàn thành!")
    print("\n💡 Cách sử dụng:")
    print("1. Import class: from lung_cancer_predictor import LungCancerPredictor")
    print("2. Tạo instance: predictor = LungCancerPredictor()")
    print("3. Dự đoán: result = predictor.predict_single(your_data)")


if __name__ == "__main__":
    main()
