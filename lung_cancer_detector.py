#!/usr/bin/env python3
"""
Lung Cancer Detection using YOLO Model
Phát hiện ung thư phổi sử dụng model YOLO

Author: AI Assistant
Date: September 2, 2025
"""

import cv2
from ultralytics import YOLO
import os
import numpy as np
from datetime import datetime
import argparse

class LungCancerDetector:
    def __init__(self, model_path="best.pt"):
        """
        Khởi tạo detector ung thư phổi
        
        Args:
            model_path (str): Đường dẫn đến model YOLO
        """
        self.model_path = model_path
        self.model = None
        self.class_names = {
            0: "Adenocarcinoma",
            1: "Small Cell Carcinoma", 
            2: "Large Cell Carcinoma",
            3: "Squamous Cell Carcinoma"
        }
        self.load_model()
    
    def load_model(self):
        """Tải model YOLO"""
        try:
            self.model = YOLO(self.model_path)
            print(f"✅ Đã tải model thành công: {self.model_path}")
            print(f"📋 Các loại ung thư được nhận diện:")
            for i, name in enumerate(self.model.names.values()):
                print(f"   {i}: {name}")
        except Exception as e:
            print(f"❌ Lỗi khi tải model: {e}")
            return False
        return True
    
    def detect_single_image(self, image_path, conf_threshold=0.25, save_result=True):
        """
        Phát hiện ung thư trong một ảnh CT scan
        
        Args:
            image_path (str): Đường dẫn đến ảnh CT scan
            conf_threshold (float): Ngưỡng confidence (0.1-1.0)
            save_result (bool): Có lưu kết quả không
        
        Returns:
            dict: Kết quả phát hiện
        """
        if not os.path.exists(image_path):
            print(f"❌ Không tìm thấy ảnh: {image_path}")
            return None
        
        print(f"\n🔍 Đang phân tích ảnh: {os.path.basename(image_path)}")
        
        try:
            # Chạy inference
            results = self.model(image_path, conf=conf_threshold)
            result = results[0]
            
            # Phân tích kết quả
            detection_info = {
                'image_path': image_path,
                'detections': [],
                'total_detections': len(result.boxes),
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            print(f"📊 Kết quả phân tích:")
            print(f"   Tổng số vùng phát hiện: {len(result.boxes)}")
            
            if len(result.boxes) > 0:
                print(f"   Chi tiết:")
                for i, box in enumerate(result.boxes):
                    class_id = int(box.cls[0])
                    confidence = float(box.conf[0])
                    class_name = self.model.names[class_id]
                    coords = box.xyxy[0].tolist()
                    
                    # Lưu thông tin detection
                    detection = {
                        'class_id': class_id,
                        'class_name': class_name,
                        'confidence': confidence,
                        'coordinates': coords,
                        'area': (coords[2] - coords[0]) * (coords[3] - coords[1])
                    }
                    detection_info['detections'].append(detection)
                    
                    print(f"   {i+1}. {class_name}")
                    print(f"      Độ tin cậy: {confidence:.2%}")
                    print(f"      Tọa độ: ({coords[0]:.0f}, {coords[1]:.0f}) -> ({coords[2]:.0f}, {coords[3]:.0f})")
                    
                    # Đánh giá mức độ nghiêm trọng
                    severity = self._assess_severity(confidence, detection['area'])
                    print(f"      Mức độ: {severity}")
                    print()
                
                # Kết luận tổng quát
                self._print_summary(detection_info)
                
            else:
                print("   ✅ Không phát hiện dấu hiệu ung thư")
            
            # Vẽ và lưu kết quả
            if save_result:
                self._save_annotated_image(result, image_path)
            
            return detection_info
            
        except Exception as e:
            print(f"❌ Lỗi khi xử lý ảnh: {e}")
            return None
    
    def _assess_severity(self, confidence, area):
        """Đánh giá mức độ nghiêm trọng"""
        if confidence > 0.8 and area > 10000:
            return "🔴 Cao (cần khám ngay)"
        elif confidence > 0.6 and area > 5000:
            return "🟡 Trung bình (cần theo dõi)"
        elif confidence > 0.3:
            return "🟢 Thấp (cần kiểm tra thêm)"
        else:
            return "⚪ Rất thấp"
    
    def _print_summary(self, detection_info):
        """In tóm tắt kết quả"""
        print("📋 TÓM TẮT KẾT QUẢ:")
        print("=" * 40)
        
        # Thống kê theo loại
        type_counts = {}
        high_risk_count = 0
        
        for det in detection_info['detections']:
            class_name = det['class_name']
            confidence = det['confidence']
            
            if class_name not in type_counts:
                type_counts[class_name] = []
            type_counts[class_name].append(confidence)
            
            if confidence > 0.6:
                high_risk_count += 1
        
        for cancer_type, confidences in type_counts.items():
            avg_conf = sum(confidences) / len(confidences)
            print(f"• {cancer_type}: {len(confidences)} vùng (độ tin cậy TB: {avg_conf:.2%})")
        
        print(f"• Vùng nguy cơ cao (>60%): {high_risk_count}")
        
        # Khuyến nghị
        if high_risk_count > 0:
            print("\n⚠️  KHUYẾN NGHỊ: Cần khám bệnh ngay lập tức!")
        elif detection_info['total_detections'] > 0:
            print("\n💡 KHUYẾN NGHỊ: Nên theo dõi và kiểm tra thêm")
        else:
            print("\n✅ KẾT QUẢ: Không phát hiện dấu hiệu bất thường")
    
    def _save_annotated_image(self, result, original_path):
        """Lưu ảnh đã được đánh dấu"""
        # Tạo ảnh với annotations
        annotated_image = result.plot()
        
        # Tạo tên file output
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.splitext(os.path.basename(original_path))[0]
        output_path = f"result_{filename}_{timestamp}.jpg"
        
        # Lưu ảnh
        cv2.imwrite(output_path, annotated_image)
        print(f"💾 Đã lưu kết quả: {output_path}")
    
    def batch_process(self, image_folder, conf_threshold=0.25):
        """
        Xử lý nhiều ảnh trong thư mục
        
        Args:
            image_folder (str): Thư mục chứa ảnh CT scan
            conf_threshold (float): Ngưỡng confidence
        """
        if not os.path.exists(image_folder):
            print(f"❌ Không tìm thấy thư mục: {image_folder}")
            return
        
        # Các định dạng ảnh được hỗ trợ
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.dicom', '.dcm'}
        
        # Tìm tất cả file ảnh
        image_files = []
        for file in os.listdir(image_folder):
            file_path = os.path.join(image_folder, file)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(file.lower())
                if ext in image_extensions:
                    image_files.append(file_path)
        
        if not image_files:
            print(f"❌ Không tìm thấy ảnh nào trong thư mục: {image_folder}")
            return
        
        print(f"📁 Tìm thấy {len(image_files)} ảnh để phân tích")
        print("=" * 60)
        
        all_results = []
        cancer_found = 0
        
        # Xử lý từng ảnh
        for i, image_path in enumerate(image_files, 1):
            print(f"[{i}/{len(image_files)}] Đang xử lý: {os.path.basename(image_path)}")
            
            result = self.detect_single_image(image_path, conf_threshold)
            if result and result['total_detections'] > 0:
                cancer_found += 1
                all_results.append(result)
            
            print("-" * 60)
        
        # Tóm tắt tổng quát
        print(f"\n📊 TỔNG KẾT PHÂN TÍCH:")
        print(f"• Tổng số ảnh đã xử lý: {len(image_files)}")
        print(f"• Số ảnh phát hiện ung thư: {cancer_found}")
        print(f"• Tỷ lệ phát hiện: {cancer_found/len(image_files)*100:.1f}%")
        
        if cancer_found > 0:
            print(f"\n⚠️  {cancer_found} ảnh có dấu hiệu bất thường cần được kiểm tra kỹ!")
    
    def get_model_info(self):
        """Hiển thị thông tin model"""
        print("📋 THÔNG TIN MODEL NHẬN DIỆN UNG THƯ PHỔI")
        print("=" * 50)
        print(f"Model path: {self.model_path}")
        print(f"Model type: YOLO11m")
        print(f"Trained epochs: 30")
        print(f"Training time: 3.496 hours")
        print(f"Classes: {len(self.model.names)}")
        
        print(f"\n📊 Độ chính xác của model:")
        print(f"• Overall mAP50: 98.2%")
        print(f"• Overall mAP50-95: 64.6%")
        print(f"\n📈 Độ chính xác theo loại:")
        print(f"• Adenocarcinoma: P=97.0%, R=98.1%, mAP50=99.0%")
        print(f"• Small Cell Carcinoma: P=96.8%, R=97.9%, mAP50=98.8%")
        print(f"• Large Cell Carcinoma: P=90.6%, R=94.0%, mAP50=95.7%")
        print(f"• Squamous Cell Carcinoma: P=97.2%, R=97.3%, mAP50=99.2%")


def main():
    """Hàm main"""
    parser = argparse.ArgumentParser(description='Lung Cancer Detection using YOLO')
    parser.add_argument('--model', default='best.pt', help='Path to YOLO model')
    parser.add_argument('--image', help='Path to single image')
    parser.add_argument('--folder', help='Path to folder containing images')
    parser.add_argument('--conf', type=float, default=0.25, help='Confidence threshold')
    parser.add_argument('--info', action='store_true', help='Show model information')
    
    args = parser.parse_args()
    
    # Khởi tạo detector
    detector = LungCancerDetector(args.model)
    
    if args.info:
        detector.get_model_info()
    elif args.image:
        detector.detect_single_image(args.image, args.conf)
    elif args.folder:
        detector.batch_process(args.folder, args.conf)
    else:
        # Interactive mode
        print("🏥 LUNG CANCER DETECTION SYSTEM")
        print("=" * 50)
        detector.get_model_info()
        
        while True:
            print("\n🎯 CHỌN CHỨC NĂNG:")
            print("1. Phân tích một ảnh CT scan")
            print("2. Phân tích nhiều ảnh trong thư mục")
            print("3. Hiển thị thông tin model")
            print("4. Thoát")
            print("-" * 30)
            
            choice = input("Nhập lựa chọn (1-4): ").strip()
            
            if choice == "1":
                image_path = input("📁 Nhập đường dẫn ảnh CT scan: ").strip()
                conf = float(input("🎯 Confidence threshold (0.1-1.0, mặc định 0.25): ") or "0.25")
                detector.detect_single_image(image_path, conf)
                
            elif choice == "2":
                folder_path = input("📁 Nhập đường dẫn thư mục chứa ảnh: ").strip()
                conf = float(input("🎯 Confidence threshold (0.1-1.0, mặc định 0.25): ") or "0.25")
                detector.batch_process(folder_path, conf)
                
            elif choice == "3":
                detector.get_model_info()
                
            elif choice == "4":
                print("👋 Cảm ơn bạn đã sử dụng hệ thống!")
                break
                
            else:
                print("❌ Lựa chọn không hợp lệ!")


if __name__ == "__main__":
    main()
