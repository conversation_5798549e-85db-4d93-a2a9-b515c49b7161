#!/usr/bin/env python3
"""
Setup script for Lung Cancer Detection System
Script cài đặt cho hệ thống dự đoán ung thư phổi

Author: AI Assistant
Date: September 2, 2025
"""

import subprocess
import sys
import os

def run_command(command):
    """Run shell command and return result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_python():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Need Python 3.8+")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    # Check if pip is available
    success, _, _ = run_command("pip --version")
    if not success:
        success, _, _ = run_command("pip3 --version")
        if not success:
            print("❌ pip not found. Please install pip first.")
            return False
        pip_cmd = "pip3"
    else:
        pip_cmd = "pip"
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        print(f"📋 Installing from requirements.txt...")
        success, stdout, stderr = run_command(f"{pip_cmd} install -r requirements.txt")
        if success:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Failed to install dependencies: {stderr}")
            return False
    else:
        print("❌ requirements.txt not found")
        return False

def check_models():
    """Check if model files exist"""
    print("\n🤖 Checking model files...")
    
    models = [
        ("model/svc_lung_cancer_new.pkl", "Survey SVC Model"),
        ("model/best.pt", "YOLO Model")
    ]
    
    all_exist = True
    for model_path, model_name in models:
        if os.path.exists(model_path):
            size = os.path.getsize(model_path) / (1024 * 1024)  # MB
            print(f"✅ {model_name}: {model_path} ({size:.1f} MB)")
        else:
            print(f"❌ {model_name}: {model_path} - NOT FOUND")
            all_exist = False
    
    return all_exist

def check_data():
    """Check if data files exist"""
    print("\n📊 Checking data files...")
    
    data_file = "data/survey lung cancer.csv"
    if os.path.exists(data_file):
        print(f"✅ Training data: {data_file}")
        return True
    else:
        print(f"❌ Training data: {data_file} - NOT FOUND")
        return False

def test_imports():
    """Test if key modules can be imported"""
    print("\n🧪 Testing imports...")
    
    modules = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("ultralytics", "YOLO"),
        ("sklearn", "Scikit-learn"),
        ("cv2", "OpenCV"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
        ("joblib", "Joblib"),
        ("tkinter", "Tkinter (GUI)")
    ]
    
    all_imported = True
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - Import failed")
            all_imported = False
    
    return all_imported

def create_run_scripts():
    """Create convenient run scripts"""
    print("\n📝 Creating run scripts...")
    
    # Backend script
    backend_script = """#!/bin/bash
echo "🏥 Starting Lung Cancer Detection API..."
python3 app.py
"""
    
    # GUI script  
    gui_script = """#!/bin/bash
echo "🖥️ Starting Lung Cancer Detection GUI..."
python3 gui.py
"""
    
    try:
        with open("run_backend.sh", "w") as f:
            f.write(backend_script)
        os.chmod("run_backend.sh", 0o755)
        print("✅ Created run_backend.sh")
        
        with open("run_gui.sh", "w") as f:
            f.write(gui_script)
        os.chmod("run_gui.sh", 0o755)
        print("✅ Created run_gui.sh")
        
        return True
    except Exception as e:
        print(f"❌ Failed to create run scripts: {e}")
        return False

def main():
    """Main setup function"""
    print("🏥 LUNG CANCER DETECTION SYSTEM - SETUP")
    print("=" * 50)
    
    # Check Python version
    if not check_python():
        print("\n❌ Setup failed: Python version too old")
        return False
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed: Could not install dependencies")
        return False
    
    # Check models
    models_ok = check_models()
    
    # Check data
    data_ok = check_data()
    
    # Test imports
    imports_ok = test_imports()
    
    # Create run scripts
    scripts_ok = create_run_scripts()
    
    print("\n" + "=" * 50)
    print("📋 SETUP SUMMARY:")
    print(f"✅ Python version: OK")
    print(f"✅ Dependencies: OK")
    print(f"{'✅' if models_ok else '⚠️'} Models: {'OK' if models_ok else 'Some missing'}")
    print(f"{'✅' if data_ok else '⚠️'} Data: {'OK' if data_ok else 'Missing'}")
    print(f"{'✅' if imports_ok else '❌'} Imports: {'OK' if imports_ok else 'Failed'}")
    print(f"{'✅' if scripts_ok else '❌'} Run scripts: {'OK' if scripts_ok else 'Failed'}")
    
    if imports_ok:
        print("\n🎉 SETUP COMPLETED SUCCESSFULLY!")
        print("\n💡 How to run:")
        print("1. Backend API: ./run_backend.sh or python3 app.py")
        print("2. Desktop GUI: ./run_gui.sh or python3 gui.py")
        print("3. Web interface: http://localhost:8000")
        return True
    else:
        print("\n❌ Setup completed with errors. Check missing dependencies.")
        return False

if __name__ == "__main__":
    main()
