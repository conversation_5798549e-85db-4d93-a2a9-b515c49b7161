#!/bin/bash

# Script để khởi chạy GUI Lung Cancer Detection

echo "🚀 Starting Lung Cancer Detection GUI..."

# Chuyển đến thư mục dự án
cd /Users/<USER>/Documents/python-file-hoc/projects/test-yolo

# Kích hoạt môi trường ảo
source venv/bin/activate

# Kiểm tra các thư viện cần thiết
echo "📦 Checking dependencies..."
python -c "import tkinter; print('✅ Tkinter OK')" 2>/dev/null || echo "⚠️  Tkinter may not be available"
python -c "import PIL; print('✅ PIL OK')" 2>/dev/null || echo "❌ PIL not found"
python -c "import cv2; print('✅ OpenCV OK')" 2>/dev/null || echo "❌ OpenCV not found"
python -c "import ultralytics; print('✅ Ultralytics OK')" 2>/dev/null || echo "❌ Ultralytics not found"

echo "🏥 Launching GUI..."

# Chạy GUI
python lung_cancer_gui.py

# Deactivate virtual environment when done
deactivate

echo "👋 GUI closed. Thank you!"
